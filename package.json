{"name": "khan_baba", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.21.0", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@reduxjs/toolkit": "^2.8.1", "expo-crypto": "^14.1.4", "react": "19.0.0", "react-native": "0.79.2", "react-native-device-info": "^14.0.4", "react-native-dropdown-picker": "^5.4.6", "react-native-gesture-handler": "^2.25.0", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-swiper": "^1.6.0", "react-native-vector-icons": "^10.2.0", "react-native-web-swiper": "^2.2.4", "react-native-webview": "^13.13.5", "react-redux": "^9.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-push-notification": "^8.1.4", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}