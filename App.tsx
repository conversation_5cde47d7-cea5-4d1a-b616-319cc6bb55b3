'use client';

import {useEffect, useState, useRef} from 'react';
import {StatusBar, View} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {Provider} from 'react-redux';
import store from './src/store';
import {ThemeProvider} from './src/theme/ThemeProvider';
import Icon from 'react-native-vector-icons/MaterialIcons'; // Replace MaterialIcons from Expo
import {loadUserData, loadLoginStatus} from './src/utils/storage';
import {initializeAuth} from './src/store/authSlice';
import PushNotification from 'react-native-push-notification'; // Replace Expo notifications

// Screens
import SplashScreen from './src/screens/SplashScreen';
import HomeScreen from './src/screens/HomeScreen';
import ShopScreen from './src/screens/ShopScreen';
import CheckoutScreen from './src/screens/CheckoutScreen';
import ItemDetailScreen from './src/screens/ItemDetailScreen';
import SearchScreen from './src/screens/SearchScreen';
import ProfileScreen from './src/screens/ProfileScreen';
import LoginScreen from './src/screens/LoginScreen';
import OrderHistoryScreen from './src/screens/OrderHistoryScreen';
import OrderDetailScreen from './src/screens/OrderDetailScreen';
import ForgotPasswordScreen from './src/screens/ForgotPasswordScreen';
import ChangePasswordScreen from './src/screens/ChangePasswordScreen';
import NotificationsScreen from './src/screens/NotificationsScreen';
import NotificationSettingsScreen from './src/screens/NotificationSettingsScreen';

const Stack = createStackNavigator();

// Create a reference to the navigation object that will be set later
const navigationRef = {current: null};

// Create a wrapper component to handle the initialization
const AppWithAuth = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [pushToken, setPushToken] = useState('');
  const navigation = useRef();

  useEffect(() => {
    const initializeAuthState = async () => {
      try {
        // Load user data and login status from AsyncStorage
        const userData = await loadUserData();
        const isLoggedIn = await loadLoginStatus();

        // Initialize Redux store with the loaded data
        store.dispatch(initializeAuth({isLoggedIn, userData}));
      } catch (error) {
        console.error('Error initializing auth state:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeAuthState();
  }, []);

  // Show nothing while initializing
  if (!isInitialized) {
    return null;
  }

  return (
    <NavigationContainer
      ref={navigatorRef => {
        navigation.current = navigatorRef;
        navigationRef.current = navigatorRef; // Set the global navigation reference
      }}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="#FFFFFF"
        translucent
      />
      <Stack.Navigator
        initialRouteName="Splash"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#000000',
            elevation: 0,
            shadowOpacity: 0,
          },
          headerTintColor: '#FFFFFF',
          headerTitleStyle: {
            fontFamily: 'Poppins-SemiBold',
            fontSize: 18,
          },
          headerTitleAlign: 'center',
        }}>
        <Stack.Screen
          name="Splash"
          component={SplashScreen}
          options={{headerShown: false}}
        />

        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="ShopStack"
          component={ShopScreen}
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="Checkout"
          component={CheckoutScreen}
          options={{
            headerTitle: 'Checkout',
            headerLeft: props => (
              <View style={{marginLeft: 8}}>
                <Icon name="arrow-back" size={24} color="#FFFFFF" {...props} />
              </View>
            ),
          }}
        />

        <Stack.Screen
          name="ItemDetail"
          component={ItemDetailScreen}
          options={{
            headerShown: false,
            presentation: 'card',
          }}
        />
        <Stack.Screen
          name="Search"
          component={SearchScreen}
          options={{
            headerShown: false,
            presentation: 'card',
          }}
        />
        <Stack.Screen
          name="Profile"
          component={ProfileScreen}
          options={{
            headerTitle: 'Profile',
            headerStyle: {
              backgroundColor: '#FFFFFF',
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: '#000000',
            headerLeft: props => (
              <View style={{marginLeft: 8}}>
                <Icon name="arrow-back" size={24} color="#FFFFFF" {...props} />
              </View>
            ),
          }}
        />
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="ForgotPasswordScreen"
          component={ForgotPasswordScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="OrderHistory"
          component={OrderHistoryScreen}
          options={{
            headerTitle: 'Order History',
            headerStyle: {
              backgroundColor: '#FFFFFF',
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: '#000000',
          }}
        />
        <Stack.Screen
          name="OrderDetail"
          component={OrderDetailScreen}
          options={{
            headerTitle: 'Order Details',
            headerStyle: {
              backgroundColor: '#FFFFFF',
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: '#000000',
          }}
        />
        <Stack.Screen
          name="ChangePassword"
          component={ChangePasswordScreen}
          options={{
            headerTitle: 'Change Password',
            headerStyle: {
              backgroundColor: '#FFFFFF',
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: '#000000',
            headerLeft: props => (
              <View style={{marginLeft: 8}}>
                <Icon name="arrow-back" size={24} color="#000000" {...props} />
              </View>
            ),
          }}
        />
        <Stack.Screen
          name="NotificationSettings"
          component={NotificationSettingsScreen}
          options={{
            headerTitle: 'Notification Settings',
            headerStyle: {
              backgroundColor: '#FFFFFF',
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: '#000000',
            headerLeft: props => (
              <View style={{marginLeft: 8}}>
                <Icon name="arrow-back" size={24} color="#000000" {...props} />
              </View>
            ),
          }}
        />
        <Stack.Screen
          name="Notifications"
          component={NotificationsScreen}
          options={{
            headerTitle: 'Notifications',
            headerStyle: {
              backgroundColor: '#FFFFFF',
              elevation: 0,
              shadowOpacity: 0,
            },
            headerTintColor: '#000000',
            headerLeft: props => (
              <View style={{marginLeft: 8}}>
                <Icon name="arrow-back" size={24} color="#000000" {...props} />
              </View>
            ),
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <AppWithAuth />
      </ThemeProvider>
    </Provider>
  );
}
