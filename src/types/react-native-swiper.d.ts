declare module 'react-native-swiper' {
  import React from 'react';
  import { ViewStyle, ScrollViewProps } from 'react-native';

  interface SwiperProps extends ScrollViewProps {
    // Basic props
    horizontal?: boolean;
    loop?: boolean;
    index?: number;
    showsButtons?: boolean;
    showsPagination?: boolean;
    
    // Autoplay
    autoplay?: boolean;
    autoplayTimeout?: number;
    autoplayDirection?: boolean;
    
    // Pagination
    paginationStyle?: ViewStyle;
    dotStyle?: ViewStyle;
    dotColor?: string;
    activeDotStyle?: ViewStyle;
    activeDotColor?: string;
    
    // Buttons
    buttonWrapperStyle?: ViewStyle;
    nextButton?: React.ReactNode;
    prevButton?: React.ReactNode;
    
    // Callbacks
    onIndexChanged?: (index: number) => void;
    
    // Other props
    scrollEnabled?: boolean;
    bounces?: boolean;
    automaticallyAdjustContentInsets?: boolean;
    removeClippedSubviews?: boolean;
    style?: ViewStyle;
  }

  export default class Swiper extends React.Component<SwiperProps> {
    scrollTo: (index: number) => void;
  }
}
