// src/components/redux/orderSlice.ts
import {createSlice, type PayloadAction} from '@reduxjs/toolkit';
import {v4 as uuidv4} from 'uuid'; // Import uuid for generating unique IDs
import {BUSINESS_ID} from '../../config'; // Import the business ID from config

// Define types for the state
interface BusinessHours {
  condition1: string;
  day: string;
  time: string;
  start_time: string;
  end_time: string;
  status: string;
  minutes_left: number;
}

interface OrderState {
  orderType: 'delivery' | 'pickup'; // Only these two possible values
  branchId: string;
  businessId: string;
  logo: string;
  username: string;
  currency: string;
  banners: string;
  allBranches: string;
  selectedBranch: string;
  uniqueOrderId: string;
  cart: any[]; // Ideally, you should type the cart items (e.g., CartItem[])
  allCategories: any[]; // Same for categories
  allItems: any[]; // Same for items
  businessHours: BusinessHours[]; // Store business hours data
  isLoggedIn: boolean; // Track authentication status
  userProfile: any; // Store user profile data
}

const initialState: OrderState = {
  orderType: 'delivery',
  branchId: '',
  businessId: BUSINESS_ID, // Use the imported business ID
  logo: '',
  username: '',
  currency: '',
  banners: '',
  allBranches: '',
  selectedBranch: '',
  uniqueOrderId: '',
  cart: [], // Initialize cart state to store the cart data
  allCategories: [],
  allItems: [],
  businessHours: [], // Initialize business hours to empty array
  isLoggedIn: false, // Initialize login status to false
  userProfile: null, // Initialize user profile to null
};

const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    setOrderType: (state, action: PayloadAction<'delivery' | 'pickup'>) => {
      console.log('setOrderType action dispatched:', action.payload);
      state.orderType = action.payload;
    },
    setBranchId: (state, action: PayloadAction<string>) => {
      console.log('setBranch action dispatched:', action.payload);
      state.branchId = action.payload;
    },
    setUniqueOrderId: state => {
      if (!state.uniqueOrderId) {
        // Generate a unique order ID using standard uuid
        state.uniqueOrderId = uuidv4();
        console.log('uuid:', state.uniqueOrderId);
      }
    },
    resetUniqueOrderId: state => {
      // Generate a unique order ID using standard uuid
      state.uniqueOrderId = uuidv4();
      console.log('uuid:', state.uniqueOrderId);
    },
    setBusinessId: (state, action: PayloadAction<string>) => {
      console.log('setBusiness:', action.payload);
      state.businessId = action.payload;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      console.log('setCurrency action dispatched:', action.payload);
      state.currency = action.payload;
    },
    setUsername: (state, action: PayloadAction<string>) => {
      console.log('setuser action dispatched:', action.payload);
      state.username = action.payload;
    },
    setLogo: (state, action: PayloadAction<string>) => {
      console.log('logo action dispatched:', action.payload);
      state.logo = action.payload;
    },
    setBanner: (state, action: PayloadAction<string>) => {
      console.log('banner action dispatched:', action.payload);
      state.banners = action.payload;
    },
    setAllBranches: (state, action: PayloadAction<string>) => {
      console.log('all branches dispatched:', action.payload);
      state.allBranches = action.payload;
    },
    setSelectedBranch: (state, action: PayloadAction<any>) => {
      console.log('selected dispatched:', action.payload);
      state.selectedBranch = action.payload;
    },
    setAllCategories: (state, action: PayloadAction<any[]>) => {
      console.log('all branches dispatched:', action.payload);
      state.allCategories = action.payload;
    },
    setAllItems: (state, action: PayloadAction<any[]>) => {
      console.log('all branches dispatched:', action.payload);
      state.allItems = action.payload;
    },
    setCartData: (state, action: PayloadAction<any[]>) => {
      state.cart = action.payload;
    },
    resetCartData: state => {
      state.cart = [];
    },
    setBusinessHours: (state, action: PayloadAction<BusinessHours[]>) => {
      console.log('business hours dispatched:', action.payload);
      state.businessHours = action.payload;
    },
    setLoginStatus: (state, action: PayloadAction<boolean>) => {
      state.isLoggedIn = action.payload;
    },
    setUserProfile: (state, action: PayloadAction<any>) => {
      state.userProfile = action.payload;
    },
    logoutUser: state => {
      state.isLoggedIn = false;
      state.userProfile = null;
    },
  },
});

export const {
  setOrderType,
  setBranchId,
  setCartData,
  setBusinessId,
  setCurrency,
  setUsername,
  setLogo,
  setBanner,
  setAllBranches,
  setSelectedBranch,
  setAllCategories,
  setAllItems,
  setUniqueOrderId,
  resetCartData,
  resetUniqueOrderId,
  setBusinessHours,
  setLoginStatus,
  setUserProfile,
  logoutUser,
} = orderSlice.actions;

export default orderSlice.reducer;
