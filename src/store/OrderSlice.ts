// src/components/redux/orderSlice.ts
import {createSlice, type PayloadAction} from '@reduxjs/toolkit';
import {v4 as uuidv4} from 'uuid'; // Import uuid for generating unique IDs
import * as Crypto from 'expo-crypto';

// Define interface for cart item
export interface CartItem {
  menu_item_id: string;
  name: string;
  price: number;
  dprice: number; // Discounted price
  dqty: number; // Quantity
  image?: string;
  desc?: string;
  options?: any[]; // Options selected for this item
  // Add other properties as needed
}

// Define interface for cart
export interface Cart {
  items: CartItem[];
  // Add other cart properties as needed
}

// Define types for the state
export interface OrderState {
  orderType: 'delivery' | 'pickup'; // Only these two possible values
  branchId: string;
  businessId: string;
  logo: string;
  username: string;
  currency: string;
  banners: string;
  allBranches: string;
  selectedBranch: any; // This should be properly typed
  uniqueOrderId: string;
  cart: Cart; // Updated to use the Cart interface
  allCategories: any[]; // Same for categories
  allItems: any[]; // Same for items
  businessHours?: any[]; // Add this property for business hours
  hasDelivery?: boolean; // Add this property
  hasPickup?: boolean; // Add this property
}

const initialState: OrderState = {
  orderType: 'delivery',
  branchId: '',
  businessId: '18',
  // businessId: "12866",
  logo: '',
  username: '',
  currency: '',
  banners: '',
  allBranches: '',
  selectedBranch: '',
  uniqueOrderId: '',
  cart: {items: []}, // Initialize cart with empty items array
  allCategories: [],
  allItems: [],
  // Note: We don't initialize hasDelivery and hasPickup here
  // as they might be set elsewhere in the application
};

const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    setOrderType: (state, action: PayloadAction<'delivery' | 'pickup'>) => {
      console.log('setOrderType action dispatched:', action.payload);
      state.orderType = action.payload;
    },
    setBranchId: (state, action: PayloadAction<string>) => {
      console.log('setBranch action dispatched:', action.payload);
      state.branchId = action.payload;
    },
    setUniqueOrderId: state => {
      if (!state.uniqueOrderId) {
        // Generate a unique order ID using uuid and random bytes
        const randomBytes = Crypto.getRandomBytes(16); // Generate 16 random bytes
        state.uniqueOrderId = uuidv4({random: randomBytes}); // Use random bytes to generate UUID
        console.log('uuid:', state.uniqueOrderId);
      }
    },
    resetUniqueOrderId: state => {
      // Generate a unique order ID using uuid and random bytes
      const randomBytes = Crypto.getRandomBytes(16); // Generate 16 random bytes
      state.uniqueOrderId = uuidv4({random: randomBytes}); // Use random bytes to generate UUID
      console.log('uuid:', state.uniqueOrderId);
    },
    setBusinessId: (state, action: PayloadAction<string>) => {
      console.log('setBusiness:', action.payload);
      state.businessId = action.payload;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      console.log('setCurrency action dispatched:', action.payload);
      state.currency = action.payload;
    },
    setUsername: (state, action: PayloadAction<string>) => {
      console.log('setuser action dispatched:', action.payload);
      state.username = action.payload;
    },
    setLogo: (state, action: PayloadAction<string>) => {
      console.log('logo action dispatched:', action.payload);
      state.logo = action.payload;
    },
    setBanner: (state, action: PayloadAction<string>) => {
      console.log('banner action dispatched:', action.payload);
      state.banners = action.payload;
    },
    setAllBranches: (state, action: PayloadAction<string>) => {
      console.log('all branches dispatched:', action.payload);
      state.allBranches = action.payload;
    },
    setSelectedBranch: (state, action: PayloadAction<any>) => {
      console.log('selected dispatched:', action.payload);
      state.selectedBranch = action.payload;
    },
    setAllCategories: (state, action: PayloadAction<any[]>) => {
      console.log('all branches dispatched:', action.payload);
      state.allCategories = action.payload;
    },
    setAllItems: (state, action: PayloadAction<any[]>) => {
      console.log('all branches dispatched:', action.payload);
      state.allItems = action.payload;
    },
    setCartData: (state, action: PayloadAction<Cart>) => {
      state.cart = action.payload;
    },
    resetCartData: state => {
      state.cart = {items: []};
    },
    // Add reducers for the new properties if needed
    setHasDelivery: (state, action: PayloadAction<boolean>) => {
      state.hasDelivery = action.payload;
    },
    setHasPickup: (state, action: PayloadAction<boolean>) => {
      state.hasPickup = action.payload;
    },
    setBusinessHours: (state, action: PayloadAction<any[]>) => {
      state.businessHours = action.payload;
    },
  },
});

export const {
  setOrderType,
  setBranchId,
  setCartData,
  setBusinessId,
  setCurrency,
  setUsername,
  setLogo,
  setBanner,
  setAllBranches,
  setSelectedBranch,
  setAllCategories,
  setAllItems,
  setUniqueOrderId,
  resetCartData,
  resetUniqueOrderId,
  setHasDelivery,
  setHasPickup,
  setBusinessHours,
} = orderSlice.actions;

export default orderSlice.reducer;
