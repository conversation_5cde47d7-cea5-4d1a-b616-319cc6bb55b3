// Typography styles for consistent text appearance

import type { TextStyle } from "react-native"

type FontWeight = "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900"

interface TypographyStyles {
  fontFamily: string
  fontWeight: FontWeight
  fontSize: number
  lineHeight: number
  letterSpacing?: number
}

// Base typography styles
const createTextStyle = (
  fontSize: number,
  fontWeight: FontWeight = "normal",
  lineHeight: number = fontSize * 1.5,
  letterSpacing?: number,
): TextStyle => ({
  fontFamily: "Inter, sans-serif",
  fontSize,
  fontWeight,
  lineHeight,
  ...(letterSpacing ? { letterSpacing } : {}),
})

export const typography = {
  // Headings
  h1: createTextStyle(28, "700", 34),
  h2: createTextStyle(24, "700", 30),
  h3: createTextStyle(20, "600", 26),
  h4: createTextStyle(18, "600", 24),
  h5: createTextStyle(16, "600", 22),

  // Body text
  bodyLarge: createTextStyle(16, "400", 24),
  bodyMedium: createTextStyle(14, "400", 22),
  bodySmall: createTextStyle(12, "400", 18),

  // Special text styles
  caption: createTextStyle(12, "400", 16, 0.4),
  button: createTextStyle(14, "600", 20, 0.5),
  label: createTextStyle(14, "500", 20),
  price: createTextStyle(16, "700", 24),
}

