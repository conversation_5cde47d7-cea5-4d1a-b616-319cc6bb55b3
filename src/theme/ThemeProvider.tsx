'use client';

import {createContext, useContext, useState} from 'react';
import {useColorScheme} from 'react-native';

// Define theme colors for white & black design
export const lightTheme = {
  primary: '#000000', // Black instead of KFC Red
  primaryDark: '#000000',
  primaryLight: '#333333',
  secondary: '#555555', // Dark gray
  background: '#FFFFFF',
  card: '#FFFFFF',
  surface: '#F8F8F8',
  error: '#B00020',
  success: '#4CAF50',
  warning: '#FFC107',
  text: '#000000',
  textSecondary: '#555555',
  textLight: '#777777',
  border: '#EEEEEE',
  divider: '#F0F0F0',
  disabled: '#CCCCCC',
  icon: '#555555',
  shadow: 'rgba(0, 0, 0, 0.1)',
};

export const darkTheme = {
  primary: '#000000', // Black
  primaryDark: '#000000',
  primaryLight: '#333333',
  secondary: '#555555',
  background: '#121212',
  card: '#1E1E1E',
  surface: '#242424',
  error: '#CF6679',
  success: '#4CAF50',
  warning: '#FFC107',
  text: '#FFFFFF',
  textSecondary: '#B0B0B0',
  textLight: '#757575',
  border: '#2C2C2C',
  divider: '#2C2C2C',
  disabled: '#5C5C5C',
  icon: '#B0B0B0',
  shadow: 'rgba(0, 0, 0, 0.3)',
};

// Define typography - KFC typically uses a cleaner, bolder font
export const typography = {
  h1: {
    fontFamily: 'Poppins-Bold',
    fontSize: 28,
    lineHeight: 34,
  },
  h2: {
    fontFamily: 'Poppins-Bold',
    fontSize: 24,
    lineHeight: 30,
  },
  h3: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 20,
    lineHeight: 26,
  },
  h4: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 18,
    lineHeight: 24,
  },
  h5: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    lineHeight: 22,
  },
  subtitle1: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    lineHeight: 22,
  },
  subtitle2: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    lineHeight: 20,
  },
  body1: {
    fontFamily: 'Poppins-Regular',
    fontSize: 16,
    lineHeight: 24,
  },
  body2: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    lineHeight: 20,
  },
  button: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.5,
  },
  caption: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    lineHeight: 16,
  },
  overline: {
    fontFamily: 'Poppins-Medium',
    fontSize: 10,
    lineHeight: 14,
    letterSpacing: 1,
    textTransform: 'uppercase',
  },
  price: {
    fontFamily: 'Poppins-Bold',
    fontSize: 16,
    lineHeight: 22,
  },
};

// Define spacing
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Define border radius - KFC uses slightly more rounded corners
export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  round: 9999,
};

// Create theme context
const ThemeContext = createContext({
  theme: lightTheme,
  typography,
  spacing,
  borderRadius,
  isDark: false,
  toggleTheme: () => {},
});

// Theme provider component
export const ThemeProvider = ({children}: {children: React.ReactNode}) => {
  const colorScheme = useColorScheme();
  const [isDark, setIsDark] = useState(colorScheme === 'dark');
  const theme = isDark ? darkTheme : lightTheme;

  const toggleTheme = () => {
    setIsDark(!isDark);
  };

  return (
    <ThemeContext.Provider
      value={{
        theme,
        typography,
        spacing,
        borderRadius,
        isDark,
        toggleTheme,
      }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme
export const useTheme = () => useContext(ThemeContext);
