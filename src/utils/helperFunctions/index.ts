'use client';

import {useState} from 'react';
import {useSelector, useDispatch} from 'react-redux';
import {setCartData} from '../../store/orderSlice'; // Adjust this import to your actual Redux slice

// Define interfaces for expected types
interface Product {
  menu_item_id: string;
  image: string;
  name: string;
  price: number;
  discount?: number;
  item_level_discount_value?: number;
  tax?: number;
  item_level_tax_value?: number;
  weight_value?: string;
  weight_unit?: string;
  comment?: string;
  menu_cat_id: string;
  product_code?: string;
  category: string;
  dname?: string;
  dprice?: number;
  category_id?: string;
  category_name?: string;
  options?: any; // Add options field
}

interface CartItem {
  id: string;
  name: string;
  price: string;
  qty: number;
  discount: number;
  item_level_discount_value: number;
  tax: string;
  item_level_tax_value: number;
  weight_value: string;
  weight_unit: string;
  comment: string;
  category_id: string;
  product_code: string;
  category_name: string;
  brand_id: string;
  options: object;
  image: string; // Add image property
}

interface CartPayload {
  action: string;
  current_date: string;
  unique_order_id: string;
  order_type: string;
  business_id: string;
  branch_id: string;
  items: CartItem[]; // Use the CartItem type here
}

export const useAddToCart = () => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState<boolean>(false); // Track loading state

  const orderType = useSelector((state: any) => state.order.orderType); // Replace `any` with correct type for your state
  const branchId = useSelector(
    (state: any) => state.order.selectedBranch.branch_id,
  ); // Replace `any` with correct type
  const uniqueOrderId = useSelector((state: any) => state.order.uniqueOrderId); // Replace `any` with correct type
  const businessId = useSelector((state: any) => state.order.businessId); // Replace `any` with correct type

  const addToCart = async (
    product: Product,
    action: string,
    type: 'new' | 'old',
    options?: any,
    qty?: any,
  ) => {
    console.log('Adding to cart with action:', action);
    console.log('Product:', JSON.stringify(product, null, 2));

    setIsLoading(true);

    let processedOptions;

    // For existing cart items (type === "old"), use the options exactly as they are
    // without any processing to preserve the structure from the API
    if (type === 'old') {
      processedOptions = product.options;
      console.log(
        'Using exact options from cart item:',
        JSON.stringify(processedOptions, null, 2),
      );
    } else {
      // For new items, handle options as before
      processedOptions = options;

      // If not provided, check if the product has options
      if (!processedOptions && product.options) {
        processedOptions = product.options;
        console.log(
          'Using product.options:',
          JSON.stringify(processedOptions, null, 2),
        );
      }

      // Handle string options
      if (typeof processedOptions === 'string') {
        try {
          processedOptions = JSON.parse(processedOptions);
          console.log(
            'Parsed options string:',
            JSON.stringify(processedOptions, null, 2),
          );
        } catch (e) {
          console.error('Error parsing options string:', e);
          processedOptions = {};
        }
      }

      // Default to empty object if still undefined
      if (!processedOptions) {
        processedOptions = {};
      }
    }

    console.log(
      'Final processed options:',
      JSON.stringify(processedOptions, null, 2),
    );

    let payload: CartPayload = {
      action: '',
      current_date: '',
      unique_order_id: '',
      order_type: '',
      business_id: '',
      branch_id: '',
      items: [],
    };

    if (type === 'new') {
      payload = {
        action: action,
        current_date: new Date().toISOString(),
        unique_order_id: uniqueOrderId,
        order_type: orderType,
        business_id: `${businessId}`,
        branch_id: `${branchId}`,
        items: [
          {
            id: String(product.menu_item_id),
            image: product.image,
            name: product.name,
            price: String(product.price),
            qty: qty ? qty : 1,
            discount: product.discount || 0,
            item_level_discount_value: product.item_level_discount_value || 0,
            tax: String(product.tax || 0),
            item_level_tax_value: product.item_level_tax_value || 0,
            weight_value: product.weight_value || '0',
            weight_unit: product.weight_unit || 'kg',
            comment: product.comment || '',
            category_id: String(product.menu_cat_id),
            product_code: product.product_code || '0',
            category_name: product.category,
            brand_id: '0',
            options: processedOptions,
          },
        ],
      };
    } else if (type === 'old') {
      payload = {
        action: action,
        current_date: new Date().toISOString(),
        unique_order_id: uniqueOrderId,
        order_type: orderType,
        business_id: `${businessId}`,
        branch_id: `${branchId}`,
        items: [
          {
            id: String(product.menu_item_id),
            name: product.dname || product.name,
            price: String(product.dprice || product.price),
            qty: qty ? qty : 1,
            discount: product.discount || 0,
            item_level_discount_value: product.item_level_discount_value || 0,
            tax: String(product.tax || 0),
            item_level_tax_value: product.item_level_tax_value || 0,
            weight_value: `${product.weight_value || '0'}`,
            weight_unit: product.weight_unit || 'kg',
            comment: product.comment || '',
            category_id: String(product.category_id || product.menu_cat_id),
            product_code: product.product_code || '0',
            category_name: product.category_name || product.category,
            brand_id: '0',
            options: processedOptions,
            image: product.image,
          },
        ],
      };
    }

    console.log('Final payload:', JSON.stringify(payload, null, 2));

    try {
      const response = await fetch(
        `https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${businessId}/cart`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server error:', errorText);
        return {
          success: false,
          message: `Server error: ${response.status}. Please try again.`,
        };
      }

      const result = await response.json();
      console.log('Cart API response:', JSON.stringify(result, null, 2));

      if (result && result.result) {
        dispatch(setCartData(result.result));
        return {
          success: true,
          cartData: result.result,
        };
      } else {
        return {
          success: false,
          message: result.message || 'Failed to add item to cart.',
        };
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      return {
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to add item to cart.',
      };
    } finally {
      setIsLoading(false);
    }
  };

  return {addToCart, isLoading}; // Return the function and loading state
};

// Update the formatSummaryPrice function to use currency symbols
export const formatSummaryPrice = (
  price: number | string | undefined,
  currency: string,
  decimalPlaces: number,
) => {
  if (!price) {
    return `${getCurrencySymbol(currency)} 0`;
  }

  const inputValue = typeof price === 'string' ? Number(price) : price;

  // use toLocaleString to format the number with commas
  const formattedPrice = inputValue?.toLocaleString(undefined, {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces,
  });

  return `${getCurrencySymbol(currency)} ${formattedPrice}`;
};

// Add the getCurrencySymbol helper function here too for consistency
export const getCurrencySymbol = (currency: string): string => {
  switch (currency?.toUpperCase()) {
    case 'USD':
      return '$';
    case 'EUR':
      return '€';
    case 'GBP':
      return '£';
    case 'JPY':
      return '¥';
    case 'INR':
      return '₹';
    case 'PKR':
    case 'RS':
    case 'RS.':
    case 'RS ':
    case 'RS. ':
    case 'RS':
      return '₨';
    default:
      return currency || '$'; // Return the currency code itself or $ as fallback
  }
};

// return string having capitalize first letter of every word
export const capitalizeFirstLetter = (inputString: string) => {
  return inputString?.replace(/\b\w/g, char => char.toUpperCase());
};
