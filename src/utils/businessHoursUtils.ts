/**
 * Utility functions for checking business hours
 */

import {Alert} from 'react-native';

/**
 * Checks if the business is currently closed based on business hours data
 * @param businessHours The business hours data from the API
 * @returns boolean indicating if the business is closed
 */
export const isBusinessClosed = (businessHours: any[]) => {
  if (
    !businessHours ||
    !Array.isArray(businessHours) ||
    businessHours.length === 0
  ) {
    console.log('No business hours data available, assuming business is open');
    return false; // If no data, assume business is open
  }

  // Log the business hours data for debugging
  console.log('Business hours data:', JSON.stringify(businessHours, null, 2));

  // Check if any of the business hours entries has condition1 === "close"
  // OR if the status is "0" (inactive/closed)
  const isClosed = businessHours.some(hours => {
    const conditionClosed = hours.condition1 === 'close';
    const statusClosed = hours.status === '0';

    console.log(
      `Hours entry - condition1: ${hours.condition1}, status: ${hours.status}`,
    );
    console.log(
      `Is closed based on condition: ${conditionClosed}, based on status: ${statusClosed}`,
    );

    return conditionClosed || statusClosed;
  });

  console.log('Final business closed status:', isClosed);
  return isClosed;
};

/**
 * Shows an alert with business hours information
 * @param businessHours The business hours data from the API
 */
export const showBusinessClosedAlert = (businessHours: any[]) => {
  if (
    !businessHours ||
    !Array.isArray(businessHours) ||
    businessHours.length === 0
  ) {
    Alert.alert('Business Closed', 'The business is currently closed.');
    return;
  }

  // Find the entry with business hours information
  const businessHoursInfo =
    businessHours.find(hours => hours.start_time && hours.end_time) ||
    businessHours[0];

  if (businessHoursInfo) {
    const startTime = businessHoursInfo.start_time || 'N/A';
    const endTime = businessHoursInfo.end_time || 'N/A';

    Alert.alert(
      'Business Closed',
      `The business is currently closed. Business hours are from ${startTime} to ${endTime}.`,
    );
  } else {
    Alert.alert('Business Closed', 'The business is currently closed.');
  }
};

/**
 * Checks if business is closed and shows an alert if it is
 * @param businessHours The business hours data from the API
 * @returns boolean indicating if the business is closed
 */
export const checkBusinessHours = (businessHours: any[]) => {
  console.log('Checking business hours...');
  const closed = isBusinessClosed(businessHours);

  if (closed) {
    console.log('Business is closed, showing alert');
    showBusinessClosedAlert(businessHours);
  } else {
    console.log('Business is open');
  }

  return closed;
};
