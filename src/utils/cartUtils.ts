'use client';

import {useState} from 'react';
import {useSelector, useDispatch} from 'react-redux';
import {setCartData} from '../store/orderSlice';
import {API_ENDPOINTS} from '../config'; // Import API endpoints from config

// Define interfaces for expected types
interface Product {
  menu_item_id: string;
  image: string;
  name: string;
  price: number;
  discount?: number;
  item_level_discount_value?: number;
  tax?: number;
  item_level_tax_value?: number;
  weight_value?: string;
  weight_unit?: string;
  comment?: string;
  menu_cat_id: string;
  product_code?: string;
  category: string;
  dname?: string;
  dprice?: number;
  category_id?: string;
  category_name?: string;
  option_set?: any; // Add options field
}

interface CartItem {
  id: string;
  name: string;
  price: string;
  qty: number;
  discount: number;
  item_level_discount_value: number;
  tax: string;
  item_level_tax_value: number;
  weight_value: string;
  weight_unit: string;
  comment: string;
  category_id: string;
  product_code: string;
  category_name: string;
  brand_id: string;
  options: object;
  image: string;
}

interface CartPayload {
  action: string;
  current_date: string;
  unique_order_id: string;
  order_type: string;
  business_id: string;
  branch_id: string;
  items: CartItem[];
}

interface AddToCartResult {
  success: boolean;
  message?: string;
  cartData?: any;
}

interface RootState {
  order: {
    orderType: string;
    selectedBranch: {
      branch_id: string;
    };
    uniqueOrderId: string;
    businessId: string;
    allItems: Product[];
  };
}

// Update the addToCart function to return cart data in the success response
export const useAddToCart = () => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const orderType = useSelector((state: RootState) => state.order.orderType);
  const branchId = useSelector(
    (state: RootState) => state.order.selectedBranch?.branch_id,
  );
  const uniqueOrderId = useSelector(
    (state: RootState) => state.order.uniqueOrderId,
  );
  const businessId = useSelector((state: RootState) => state.order.businessId);
  const allItems = useSelector((state: RootState) => state.order.allItems);

  const addToCart = async (
    product: Product,
    action: string,
    type: 'new' | 'old',
    options?: any,
    qty?: number,
  ): Promise<AddToCartResult> => {
    setIsLoading(true);

    console.log('CartUtils - Adding to cart with action:', action);
    console.log('CartUtils - Product:', JSON.stringify(product, null, 2));

    let processedOptions;
    let formattedOptions = {};

    // For existing cart items (type === "old"), use the options exactly as they are
    // without any processing to preserve the structure from the API
    if (type === 'old') {
      formattedOptions = JSON.parse(product.option_set || '{}');
      processedOptions = formattedOptions;
      console.log(
        'CartUtils - Using exact options from cart item:',
        JSON.stringify(processedOptions, null, 2),
      );
    } else {
      // For new items, handle options as before
      processedOptions = options;

      // If not provided, check if the product has options
      if (!processedOptions && product.option_set) {
        formattedOptions = JSON.parse(product.option_set);
        processedOptions = formattedOptions;
        console.log(
          'CartUtils - Using product.options:',
          JSON.stringify(processedOptions, null, 2),
        );
      }

      // Handle string options
      if (typeof processedOptions === 'string') {
        try {
          processedOptions = JSON.parse(processedOptions);
          console.log(
            'CartUtils - Parsed options string:',
            JSON.stringify(processedOptions, null, 2),
          );
        } catch (e) {
          console.error('CartUtils - Error parsing options string:', e);
          processedOptions = {};
        }
      }

      // Default to empty object if still undefined
      if (!processedOptions) {
        processedOptions = {};
      }
    }

    console.log(
      'CartUtils - Final processed options:',
      JSON.stringify(processedOptions, null, 2),
    );

    let payload: CartPayload = {
      action: '',
      current_date: '',
      unique_order_id: '',
      order_type: '',
      business_id: '',
      branch_id: '',
      items: [],
    };

    if (type === 'new') {
      payload = {
        action: action,
        current_date: new Date().toISOString(),
        unique_order_id: uniqueOrderId,
        order_type: orderType,
        business_id: `${businessId}`,
        branch_id: `${branchId}`,
        items: [
          {
            id: product.menu_item_id,
            image: product.image,
            name: product.name,
            price: String(product.price),
            qty: qty ? qty : 1,
            discount: product.discount || 0,
            item_level_discount_value: product.item_level_discount_value || 0,
            tax: String(product.tax || 0),
            item_level_tax_value: product.item_level_tax_value || 0,
            weight_value: product.weight_value || '0',
            weight_unit: product.weight_unit || 'kg',
            comment: product.comment || '',
            category_id: product.menu_cat_id,
            product_code: product.product_code || '0',
            category_name: product.category,
            brand_id: '0',
            options: processedOptions,
          },
        ],
      };
    } else if (type === 'old') {
      payload = {
        action: action,
        current_date: new Date().toISOString(),
        unique_order_id: uniqueOrderId,
        order_type: orderType,
        business_id: `${businessId}`,
        branch_id: `${branchId}`,
        items: [
          {
            id: product.menu_item_id,
            name: product.dname || product.name,
            price: String(product.dprice || product.price),
            qty: qty ? qty : 1,
            discount: product.discount || 0,
            item_level_discount_value: product.item_level_discount_value || 0,
            tax: String(product.tax || 0),
            item_level_tax_value: product.item_level_tax_value || 0,
            weight_value: `${product.weight_value || '0'}`,
            weight_unit: product.weight_unit || 'kg',
            comment: product.comment || '',
            category_id: product.category_id || product.menu_cat_id,
            product_code: product.product_code || '0',
            category_name: product.category_name || product.category,
            brand_id: '0',
            options: processedOptions,
            image: product.image,
          },
        ],
      };
    }

    console.log('CartUtils - Final payload:', JSON.stringify(payload, null, 2));

    try {
      const response = await fetch(`${API_ENDPOINTS.CART}/${businessId}/cart`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('CartUtils - Server error:', errorText);
        return {
          success: false,
          message: `Server error: ${response.status}. Please try again.`,
        };
      }

      const result = await response.json();
      console.log(
        'CartUtils - Cart API response:',
        JSON.stringify(result, null, 2),
      );

      if (result && result.result) {
        // Process the result to ensure images are attached
        if (result.result.items && result.result.items.length > 0) {
          result.result.items = result.result.items.map((item: any) => {
            // Find matching product in allItems
            const matchingProduct = allItems.find(
              product => product.menu_item_id === item.menu_item_id,
            );

            // If matching product found and has a valid image, use it
            if (
              matchingProduct &&
              matchingProduct.image &&
              !matchingProduct.image.includes('no_image')
            ) {
              return {
                ...item,
                image: matchingProduct.image,
              };
            }

            // If item already has a valid image, keep it
            if (item.image && !item.image.includes('no_image')) {
              return item;
            }

            // Default fallback image
            return {
              ...item,
              image:
                'https://static.tossdown.com/images/574664e6-d1c3-408e-a13d-024aa020a669.webp',
            };
          });
        }

        dispatch(setCartData(result.result));
        return {
          success: true,
          cartData: result.result,
        };
      } else {
        return {
          success: false,
          message: result.message || 'Failed to add item to cart.',
        };
      }
    } catch (error) {
      console.error('CartUtils - Error adding to cart:', error);
      return {
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Failed to add item to cart.',
      };
    } finally {
      setIsLoading(false);
    }
  };

  return {addToCart, isLoading};
};

// Update the formatPrice function to only show decimal places when they're greater than 0
export const formatPrice = (
  price: number | string | undefined,
  currency: string,
  decimalPlaces = 2,
) => {
  if (!price) {
    return `${getCurrencySymbol(currency)} 0`;
  }

  const inputValue = typeof price === 'string' ? Number(price) : price;

  // Check if the value has decimal places
  const hasDecimalPlaces = inputValue % 1 !== 0;

  // Use toLocaleString with appropriate decimal places
  const formattedPrice = inputValue?.toLocaleString(undefined, {
    minimumFractionDigits: hasDecimalPlaces ? decimalPlaces : 0,
    maximumFractionDigits: hasDecimalPlaces ? decimalPlaces : 0,
  });

  return `${getCurrencySymbol(currency)} ${formattedPrice}`;
};

// Add a helper function to convert currency codes to symbols
export const getCurrencySymbol = (currency: string): string => {
  switch (currency?.toUpperCase()) {
    case 'USD':
      return '$';
    case 'EUR':
      return '€';
    case 'GBP':
      return '£';
    case 'JPY':
      return '¥';
    case 'INR':
      return '₹';
    case 'PKR':
    case 'RS':
    case 'RS.':
    case 'RS ':
    case 'RS. ':
    case 'RS':
      return '₨';
    default:
      return currency || '$'; // Return the currency code itself or $ as fallback
  }
};

// Capitalize first letter of every word
export const capitalizeFirstLetter = (inputString: string) => {
  if (!inputString) return '';
  return inputString.replace(/\b\w/g, char => char.toUpperCase());
};
