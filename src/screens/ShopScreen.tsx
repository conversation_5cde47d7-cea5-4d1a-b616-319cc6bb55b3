'use client';
import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  ToastAndroid,
  Platform,
  Alert,
  ScrollView,
  Modal,
} from 'react-native';
import {MaterialIcons} from '@expo/vector-icons';
import {useSelector, useDispatch} from 'react-redux';
import {useNavigation, useRoute} from '@react-navigation/native';
import type {StackNavigationProp} from '@react-navigation/stack';
import type {RouteProp} from '@react-navigation/native';
import {
  setSelectedBranch,
  setOrderType,
  resetCartData,
  resetUniqueOrderId,
} from '../store/orderSlice';
import {useAddToCart, formatPrice} from '../utils/cartUtils';
import {useTheme} from '../theme/ThemeProvider';
import CartScreen from './CartScreen';
import {API_ENDPOINTS} from '../config';
import CustomDrawer from '../components/CustomDrawer';
import {Card} from '../components/ui/Card';
import {Button} from '../components/ui/BUtton';
import DropDownPicker from 'react-native-dropdown-picker';
import type {ItemType} from 'react-native-dropdown-picker';
import {checkBusinessHours} from '../utils/businessHoursUtils';
// Import Swiper
import Swiper from 'react-native-swiper';
import type {RootState} from '../store';

const {width} = Dimensions.get('window');

// Define the navigation stack type
type RootStackParamList = {
  Home: undefined;
  ItemDetail: {item: any};
  Search: undefined;
  ShopStack: {selectedCategoryId?: string} | undefined;
  Checkout: undefined;
  Profile: undefined;
  Login: undefined;
  ForgotPasswordScreen: undefined;
  Splash: undefined;
  OrderDetail: {orderId: string};
  Notifications: undefined;
  NotificationSettings: undefined;
};

// No custom type needed as we're using ItemType directly

// Define product interface
interface Product {
  menu_item_id: string;
  name: string;
  price: number;
  discount?: number;
  desc?: string;
  image: string;
  options?: any[];
  menu_cat_id: string;
  category: string;
  category_id?: string;
  category_name?: string;
  total?: string;
  dqty?: number;
}

// Define category interface
interface Category {
  category_id: string;
  category_name: string;
}

// Define cart item interface
interface CartItem {
  menu_item_id: string;
  dqty: number;
  [key: string]: any;
}

// Helper function to strip HTML tags
const stripHtmlTags = (html: string): string => {
  if (!html) return '';
  return html.replace(/(<([^>]+)>)/gi, '');
};

const ShopScreen = () => {
  const {theme, typography} = useTheme();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const route = useRoute<RouteProp<RootStackParamList, 'ShopStack'>>();
  const dispatch = useDispatch();
  const categoryScrollViewRef = useRef<ScrollView>(null);
  const swiperRef = useRef<Swiper>(null);

  // Get the selected category ID from navigation params if available
  const initialCategoryId = route.params?.selectedCategoryId || 'all';

  // State
  // Add a new state variable to track whether the category change was initiated by a tab click
  const [selectedCategory, setSelectedCategory] =
    useState<string>(initialCategoryId);
  const [loadingItems, setLoadingItems] = useState<Record<string, boolean>>({});
  const [isCartVisible, setIsCartVisible] = useState<boolean>(false);
  const [isDrawerVisible, setIsDrawerVisible] = useState<boolean>(false);
  const [isModalVisible, setModalVisible] = useState<boolean>(false);
  const [showToast, setShowToast] = useState<boolean>(false);
  const [toastMessage, setToastMessage] = useState<string>('');
  const [toastType, setToastType] = useState<string>('success'); // success or error
  // Add this new state variable after the other state declarations

  // Add this ref after the other refs
  const isTabClickRef = useRef<boolean>(false);

  // Branch selector dropdown state
  const [open, setOpen] = useState<boolean>(false);
  const [items, setItems] = useState<ItemType<string | null>[]>([]);
  const [selectedBranchId, setSelectedBranchId] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Add a state variable to track if multiple branches are available
  const [hasMultipleBranches, setHasMultipleBranches] =
    useState<boolean>(false);

  // Redux state
  const {
    allCategories,
    allItems,
    selectedBranch,
    businessId,
    orderType,
    hasDelivery,
    hasPickup,
    currency,
    cart,
    logo,
    businessHours,
  } = useSelector((state: RootState) => state.order);

  const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);

  // Create a combined categories array with "all" as the first option
  const combinedCategories = [
    {category_id: 'all', category_name: 'All'},
    ...allCategories,
  ];

  // Use the useAddToCart hook
  const {addToCart} = useAddToCart();

  useEffect(() => {
    fetchBranches();
  }, []);

  // Scroll to the selected category when the component mounts
  useEffect(() => {
    if (initialCategoryId !== 'all' && categoryScrollViewRef.current) {
      // Find the index of the selected category
      const categoryIndex = combinedCategories.findIndex(
        cat => cat.category_id === initialCategoryId,
      );

      if (categoryIndex > 0) {
        // Add a small delay to ensure the ScrollView is rendered
        setTimeout(() => {
          categoryScrollViewRef.current?.scrollTo({
            x: categoryIndex * 100 - 50, // Approximate position
            animated: true,
          });
        }, 300);
      }
    }
  }, [initialCategoryId, combinedCategories]);

  const fetchBranches = async (): Promise<void> => {
    try {
      const response = await fetch(
        `${API_ENDPOINTS.BRANCHES}?source=app&restaurant_id=${businessId}`,
      );
      const data = await response.json();

      // Check if there are multiple branches
      setHasMultipleBranches(data.result.length > 1);

      const branchItems = data.result.map((branch: any) => ({
        label: `${branch.address}, ${branch.area}, ${branch.city}`,
        value: branch.branch_id,
      }));
      setItems([{label: 'Select a branch', value: null}, ...branchItems]);

      // If there's only one branch, select it automatically without showing the modal
      if (data.result.length === 1) {
        const singleBranch = data.result[0];
        dispatch(setSelectedBranch(singleBranch));
        setSelectedBranchId(singleBranch.branch_id);
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
    }
  };

  // Function to handle branch selection is now handled inline in onProceed

  // Calculate total item count for cart badge
  const getTotalItemCount = (): number => {
    if (cart && cart.items) {
      return cart.items.reduce(
        (total: number, item: CartItem) => total + (item.dqty || 0),
        0,
      );
    }
    return 0;
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string): void => {
    // Set the ref to indicate this change was initiated by a tab click
    isTabClickRef.current = true;

    // Update the selected category state
    setSelectedCategory(categoryId);

    // Find the index of the selected category
    const index = combinedCategories.findIndex(
      cat => cat.category_id === categoryId,
    );

    // Use the swiper's scrollTo method to move to the correct position
    if (index !== -1 && swiperRef.current) {
      swiperRef.current.scrollTo(index);
    }

    // Scroll the category tabs to show the selected tab
    if (categoryScrollViewRef.current) {
      // Calculate a position that centers the selected tab
      const tabWidth = 100; // Approximate width of a tab
      const screenWidth = width;
      const scrollPosition = Math.max(
        0,
        index * tabWidth - screenWidth / 2 + tabWidth / 2,
      );

      categoryScrollViewRef.current.scrollTo({
        x: scrollPosition,
        animated: true,
      });
    }

    // Reset the ref after a short delay to allow the swiper to complete its animation
    setTimeout(() => {
      isTabClickRef.current = false;
    }, 300);
  };

  // Navigation between categories is handled by the Swiper component and handleCategorySelect function

  // Show toast message
  const showToastMessage = (
    message: string,
    type: string = 'success',
  ): void => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 2000);
  };

  // Handle add to cart
  const handleAddToCart = async (product: Product): Promise<void> => {
    // Check if business is closed
    if (businessHours && checkBusinessHours(businessHours)) {
      return; // Don't proceed if business is closed
    }

    setLoadingItems(prevState => ({
      ...prevState,
      [product.menu_item_id]: true,
    }));

    // Check if the item has options
    const hasValidOptions = product.options && product.options.length > 0;

    if (hasValidOptions) {
      // If item has options, navigate to detail screen
      navigation.navigate('ItemDetail', {item: product});
      setLoadingItems(prevState => ({
        ...prevState,
        [product.menu_item_id]: false,
      }));
      return;
    }

    // If no options, add directly to cart
    const result = await addToCart(product, 'add', 'new');

    if (!result.success) {
      Alert.alert('Error', result.message);
    } else {
      // Show toast message
      if (Platform.OS === 'android') {
        ToastAndroid.show('Item added successfully', ToastAndroid.SHORT);
      } else {
        showToastMessage('Item added successfully', 'success');
      }
    }

    setLoadingItems(prevState => ({
      ...prevState,
      [product.menu_item_id]: false,
    }));
  };

  const onProceed = (): void => {
    if (!selectedBranchId) {
      setErrorMessage('Please select a branch to proceed.');
      return;
    }

    dispatch(
      setSelectedBranch(items.find(item => item.value === selectedBranchId)),
    );
    setModalVisible(false);
  };

  // Handle cart closed with empty cart
  const handleCartClosed = (isEmpty?: boolean): void => {
    setIsCartVisible(false);
    if (isEmpty) {
      showToastMessage('Cart empty', 'error');
    }
  };

  // Render product item - using the same card as HomeScreen
  const renderProductItem = ({
    item,
    index,
  }: {
    item: Product;
    index: number;
  }): React.ReactElement => {
    const isItemLoading = loadingItems[item.menu_item_id];
    const hasDiscount = item.discount && item.discount > 0;
    const discountedPrice =
      hasDiscount && item.discount
        ? item.price - item.price * (item.discount / 100)
        : item.price;

    return (
      <TouchableOpacity
        key={item.menu_item_id || index}
        style={styles.productCard}
        onPress={() => navigation.navigate('ItemDetail', {item})}
        activeOpacity={0.9}>
        {/* Product Image */}
        <View style={styles.productImageContainer}>
          {item.image && !item.image.includes('no_image') ? (
            <Image
              source={{uri: item.image}}
              style={styles.productImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.noImageContainer}>
              {logo ? (
                <Image
                  source={{uri: logo}}
                  style={{width: 40, height: 40}}
                  resizeMode="contain"
                />
              ) : (
                <MaterialIcons name="image" size={30} color="#666" />
              )}
            </View>
          )}
        </View>

        {/* Product Title */}
        <Text
          style={styles.productTitle}
          numberOfLines={1}
          ellipsizeMode="tail">
          {item.name}
        </Text>

        {/* Product Description */}
        {item.desc ? (
          <Text
            style={styles.productDescription}
            numberOfLines={2}
            ellipsizeMode="tail">
            {stripHtmlTags(item.desc)}
          </Text>
        ) : (
          <View style={{height: 0}} /> // Zero height placeholder when no description
        )}

        {/* Price and Add Button Row */}
        <View style={styles.priceActionRow}>
          <View style={styles.priceContainer}>
            {hasDiscount && (
              <Text style={styles.originalPrice}>
                {formatPrice(item.price, currency, 0)}
              </Text>
            )}
            <Text style={styles.currentPrice}>
              {formatPrice(discountedPrice, currency, 0)}
            </Text>
          </View>

          <TouchableOpacity
            style={styles.addButton}
            onPress={e => {
              e.stopPropagation();
              handleAddToCart(item);
            }}
            disabled={isItemLoading}>
            {isItemLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <MaterialIcons name="add" size={20} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  // Render category page
  const renderCategoryPage = (
    category: Category,
    index: number,
  ): React.ReactElement => {
    const items =
      category.category_id === 'all'
        ? allItems
        : allItems.filter(
            (item: Product) => item.menu_cat_id === category.category_id,
          );

    return (
      <View key={index} style={styles.swiperSlide}>
        {items.length > 0 ? (
          <FlatList
            data={items}
            renderItem={renderProductItem}
            keyExtractor={(item: Product) => item.menu_item_id}
            numColumns={2}
            contentContainerStyle={styles.productGrid}
            columnWrapperStyle={styles.productRow}
          />
        ) : (
          <View style={styles.emptyStateContainer}>
            <MaterialIcons name="inventory" size={64} color="#CCCCCC" />
            <Text style={styles.emptyStateText}>No items available</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Header with location and toggle - same as HomeScreen */}
      <View style={styles.headerContainer}>
        {/* Left side with menu and location */}
        <View style={styles.headerLeft}>
          <TouchableOpacity
            onPress={() => setIsDrawerVisible(true)}
            style={styles.menuButton}>
            <MaterialIcons name="menu" size={24} color="#000000" />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              // Only proceed if there are multiple branches
              if (hasMultipleBranches) {
                // Check if cart is empty
                const isCartEmpty = !cart?.items || cart.items.length === 0;

                if (!isCartEmpty) {
                  // If cart is not empty, show confirmation alert
                  Alert.alert(
                    'Change Branch',
                    'Changing the branch will empty your cart. Do you want to continue?',
                    [
                      {
                        text: 'No',
                        style: 'cancel',
                      },
                      {
                        text: 'Yes',
                        onPress: () => {
                          // Empty the cart and reset unique order ID
                          dispatch(resetCartData());
                          dispatch(resetUniqueOrderId());
                          navigation.replace('Splash');
                        },
                      },
                    ],
                  );
                } else {
                  // If cart is empty, directly navigate to splash screen
                  navigation.replace('Splash');
                }
              }
            }}
            style={[
              styles.locationSelector,
              !hasMultipleBranches && styles.disabledLocationSelector,
            ]}>
            <Text style={styles.deliveryLabel}>
              {orderType === 'delivery' ? 'Delivering from' : 'Pickup from'}
              {hasMultipleBranches && (
                <MaterialIcons
                  name="keyboard-arrow-down"
                  size={16}
                  color="#000000"
                />
              )}
            </Text>
            <Text style={styles.locationText} numberOfLines={1}>
              {selectedBranch && selectedBranch.address
                ? selectedBranch.address
                : 'Select location'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Right side with search icon */}
        <TouchableOpacity
          style={styles.searchButton}
          onPress={() => navigation.navigate('Search')}>
          <MaterialIcons name="search" size={24} color="#000000" />
        </TouchableOpacity>
      </View>

      {/* Category Filters */}
      <View style={styles.categoriesContainer}>
        <ScrollView
          ref={categoryScrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesScrollContent}>
          {combinedCategories.map((category: Category) => (
            <TouchableOpacity
              key={category.category_id}
              style={[
                styles.categoryButton,
                selectedCategory === category.category_id &&
                  styles.selectedCategoryButton,
              ]}
              onPress={() => handleCategorySelect(category.category_id)}>
              <Text
                style={[
                  styles.categoryButtonText,
                  selectedCategory === category.category_id &&
                    styles.selectedCategoryButtonText,
                ]}>
                {category.category_name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Product Grid with Swiper for smooth swiping */}
      <View style={styles.productGridContainer}>
        <Swiper
          ref={swiperRef}
          showsButtons={false}
          showsPagination={false}
          loop={false}
          index={combinedCategories.findIndex(
            cat => cat.category_id === selectedCategory,
          )}
          onIndexChanged={(index: number) => {
            // Only process this if the change was NOT initiated by a tab click
            if (
              !isTabClickRef.current &&
              index >= 0 &&
              index < combinedCategories.length
            ) {
              const newCategoryId = combinedCategories[index].category_id;

              // Update the selected category
              setSelectedCategory(newCategoryId);

              // Scroll to the selected category in the horizontal ScrollView
              if (categoryScrollViewRef.current) {
                // Calculate a position that centers the selected tab
                const tabWidth = 100; // Approximate width of a tab
                const screenWidth = width;
                const scrollPosition = Math.max(
                  0,
                  index * tabWidth - screenWidth / 2 + tabWidth / 2,
                );

                categoryScrollViewRef.current.scrollTo({
                  x: scrollPosition,
                  animated: true,
                });
              }
            }
          }}
          horizontal={true}
          automaticallyAdjustContentInsets={false}
          scrollEnabled={true}
          bounces={false}
          removeClippedSubviews={false}>
          {combinedCategories.map((category: Category, index: number) =>
            renderCategoryPage(category, index),
          )}
        </Swiper>
      </View>

      {/* Toast Message */}
      {showToast && (
        <View
          style={[
            styles.toast,
            toastType === 'success' ? styles.successToast : styles.errorToast,
          ]}>
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}

      {/* Bottom Navigation */}
      <View style={styles.bottomNavigation}>
        <TouchableOpacity
          style={styles.navItem}
          onPress={() => navigation.replace('Home')}>
          <MaterialIcons name="home" size={24} color="#AAAAAA" />
          <Text style={styles.navItemText}>Home</Text>
        </TouchableOpacity>

        <View style={styles.navItem}>
          <MaterialIcons name="restaurant-menu" size={24} color="#000000" />
          <Text style={styles.navItemTextActive}>Menu</Text>
        </View>

        <TouchableOpacity
          style={styles.navItem}
          onPress={() => setIsCartVisible(true)}>
          <View style={styles.cartIconContainer}>
            <MaterialIcons name="shopping-cart" size={24} color="#AAAAAA" />
            {getTotalItemCount() > 0 && (
              <View style={styles.cartBadge}>
                <Text style={styles.cartBadgeText}>{getTotalItemCount()}</Text>
              </View>
            )}
          </View>
          <Text style={styles.navItemText}>Cart</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.navItem}
          onPress={() => navigation.navigate(isLoggedIn ? 'Profile' : 'Login')}>
          <MaterialIcons name="person" size={24} color="#AAAAAA" />
          <Text style={styles.navItemText}>Profile</Text>
        </TouchableOpacity>
      </View>

      {/* Cart Modal */}
      <CartScreen
        visible={isCartVisible}
        onClose={handleCartClosed}
        onProceedToCheckout={() => navigation.navigate('Checkout')}
      />

      {/* Branch Selection Modal - same as HomeScreen */}
      <Modal
        transparent={true}
        visible={isModalVisible}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalContainer}>
          <Card style={styles.modalContent} elevation={3}>
            <Text
              style={[
                styles.modalTitle,
                {color: theme.text, ...typography.h3},
              ]}>
              Choose Your Preference
            </Text>

            <View style={styles.toggleContainer}>
              <View style={[styles.iconToggle, {borderColor: theme.border}]}>
                {hasDelivery && (
                  <TouchableOpacity
                    onPress={() => dispatch(setOrderType('delivery'))}
                    style={[
                      styles.iconButton,
                      orderType === 'delivery'
                        ? {backgroundColor: theme.primary}
                        : {backgroundColor: theme.surface},
                    ]}>
                    <Text
                      style={[
                        styles.iconLabel,
                        orderType === 'delivery'
                          ? {color: '#FFFFFF', fontWeight: 'bold'}
                          : {color: theme.textSecondary},
                      ]}>
                      Delivery
                    </Text>
                  </TouchableOpacity>
                )}

                {hasPickup && (
                  <TouchableOpacity
                    onPress={() => dispatch(setOrderType('pickup'))}
                    style={[
                      styles.iconButton,
                      orderType === 'pickup'
                        ? {backgroundColor: theme.primary}
                        : {backgroundColor: theme.surface},
                    ]}>
                    <Text
                      style={[
                        styles.iconLabel,
                        orderType === 'pickup'
                          ? {color: '#FFFFFF', fontWeight: 'bold'}
                          : {color: theme.textSecondary},
                      ]}>
                      Pickup
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>

            <Text
              style={[
                styles.sectionTitle,
                {color: theme.text, ...typography.h5},
              ]}>
              Select Branch
            </Text>

            <View style={styles.pickerContainer}>
              <DropDownPicker
                open={open}
                value={selectedBranchId}
                items={items}
                setOpen={setOpen}
                setValue={setSelectedBranchId}
                setItems={setItems}
                placeholder="Select a branch"
                style={[
                  styles.dropdown,
                  {
                    backgroundColor: theme.surface,
                    borderColor: theme.border,
                  },
                ]}
                dropDownContainerStyle={[
                  styles.dropdownContainer,
                  {
                    backgroundColor: theme.surface,
                    borderColor: theme.border,
                  },
                ]}
                textStyle={[styles.dropdownText, {color: theme.text}]}
                placeholderStyle={[
                  styles.placeholderText,
                  {color: theme.textLight},
                ]}
                onChangeValue={(value: any) => {
                  setSelectedBranchId(value);
                  if (value) {
                    setErrorMessage('');
                  }
                }}
                listItemLabelStyle={[styles.listItemLabel, {color: theme.text}]}
                selectedItemLabelStyle={[
                  styles.selectedItemLabel,
                  {color: theme.primary},
                ]}
                maxHeight={150}
              />
            </View>

            {errorMessage !== '' && (
              <Text style={[styles.errorText, {color: theme.error}]}>
                {errorMessage}
              </Text>
            )}

            <Button
              title="Start My Order"
              onPress={onProceed}
              disabled={!selectedBranchId}
              fullWidth
              size="large"
              style={styles.startButton}
            />
          </Card>
        </View>
      </Modal>

      {/* Custom Drawer - same as HomeScreen */}
      <CustomDrawer
        visible={isDrawerVisible}
        onClose={() => setIsDrawerVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuButton: {
    padding: 4,
    marginRight: 12,
  },
  locationSelector: {
    flex: 1,
  },
  deliveryLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000000',
  },
  searchButton: {
    padding: 8,
  },
  categoriesContainer: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  categoriesScrollContent: {
    paddingHorizontal: 16,
  },
  categoryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#F5F5F5',
  },
  selectedCategoryButton: {
    backgroundColor: '#000000',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  selectedCategoryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  productGridContainer: {
    flex: 1,
    position: 'relative',
  },
  swiperSlide: {
    flex: 1,
  },
  productGrid: {
    padding: 8,
  },
  productRow: {
    justifyContent: 'space-between',
  },
  // Using the same product card styles as HomeScreen
  productCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    overflow: 'hidden',
    width: (width - 36) / 2, // Adjust for padding and gap
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    margin: 1,
  },
  productImageContainer: {
    width: '100%',
    height: 140,
    backgroundColor: '#F8F8F8',
  },
  productImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  noImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  productTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginTop: 8,
    marginBottom: 4,
    paddingHorizontal: 8,
  },
  productDescription: {
    fontSize: 12,
    color: '#666666',
    paddingHorizontal: 8,
    marginBottom: 4,
    lineHeight: 16,
    height: 32,
  },
  priceActionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingBottom: 8,
    marginTop: 16,
  },
  priceContainer: {
    flexDirection: 'column',
  },
  originalPrice: {
    fontSize: 14,
    color: '#888888',
    textDecorationLine: 'line-through',
  },
  currentPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  toast: {
    position: 'absolute',
    bottom: 80,
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 100,
    alignItems: 'center',
    marginBottom: 40, // Added bottom margin
  },
  successToast: {
    backgroundColor: 'rgba(0, 128, 0, 1)',
  },
  errorToast: {
    backgroundColor: 'rgba(255, 0, 0, 1)',
  },
  toastText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  bottomNavigation: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  navItemText: {
    fontSize: 12,
    color: '#AAAAAA',
    marginTop: 4,
  },
  navItemTextActive: {
    fontSize: 12,
    color: '#000000',
    fontWeight: 'bold',
    marginTop: 4,
  },
  cartIconContainer: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  cartBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Modal styles for branch selection
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    padding: 24,
    minHeight: 380,
  },
  modalTitle: {
    marginBottom: 24,
    textAlign: 'center',
  },
  iconToggle: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  iconButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    width: 120,
  },
  iconLabel: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  pickerContainer: {
    marginBottom: 16,
    zIndex: 1000,
  },
  dropdown: {
    borderRadius: 8,
    minHeight: 50,
  },
  dropdownContainer: {
    borderRadius: 8,
  },
  dropdownText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  placeholderText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  listItemLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  selectedItemLabel: {
    fontFamily: 'Poppins-Medium',
    fontWeight: '600',
  },
  errorText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    marginBottom: 16,
  },
  startButton: {
    marginTop: 8,
  },
  toggleContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    marginTop: 16,
    marginBottom: 12,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 100, // Add padding to account for bottom navigation
  },
  emptyStateText: {
    fontSize: 18,
    color: '#666666',
    marginTop: 16,
    fontWeight: '500',
  },
  disabledLocationSelector: {
    opacity: 0.8,
  },
});

export default ShopScreen;
