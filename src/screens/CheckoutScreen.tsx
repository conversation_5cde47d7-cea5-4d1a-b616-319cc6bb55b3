'use client';
import {
  View,
  StyleSheet,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {WebView} from 'react-native-webview'; // Import from react-native-webview
import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {resetCartData, resetUniqueOrderId} from '../store/orderSlice/index';
import {useState, useEffect} from 'react';
import Icon from 'react-native-vector-icons/MaterialIcons'; // Replace MaterialIcons from Expo
import {Platform} from 'react-native';

const CheckoutScreen = () => {
  const [isLoading, setIsLoading] = useState(true);
  const navigation: any = useNavigation();
  const dispatch = useDispatch();

  // Add these lines to set the header styling when component mounts
  useEffect(() => {
    navigation.setOptions({
      headerStyle: {
        backgroundColor: '#FFFFFF',
        elevation: 1,
        shadowOpacity: 0.1,
      },
      headerTintColor: '#000000',
      headerLeft: () => (
        <TouchableOpacity
          style={{marginLeft: 8}}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  const orderType = useSelector((state: any) => state.order.orderType);
  const branchId = useSelector(
    (state: any) => state.order.selectedBranch.branch_id,
  );
  const uniqueOrderId = useSelector((state: any) => state.order.uniqueOrderId);
  const businessId = useSelector((state: any) => state.order.businessId);

  const businessInfo = {
    cartId: uniqueOrderId,
    businessId: businessId,
    orderType: orderType,
    branchId: branchId,
    source: 'app',
    websiteLink: 'https://checkout.ordrz.com/',
  };

  // const handleLoad = () => {
  //   setIsLoading(false); // Hide loading indicator once the WebView is loaded
  // };

  const getHtmlContent = () => {
    return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Checkout Page</title>
           <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&amp;family=Montserrat:ital,wght@0,100..900;1,100..900&amp;family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&amp;display=swap" rel="stylesheet">
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&amp;family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&amp;display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://checkout-staging.ordrz.com/static/css/main.a666252e.css">

        <link rel="preload" as="script" href="https://checkout.ordrz.com/static/js/main.b75be690.js">
        <link rel="preload" as="script" href="https://checkout.ordrz.com/static/js/179.c3afeb96.chunk.js">
        <style>
         * {
    -web-kit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0;
    font-family: Inter, sans-serif;
}
          body {
            margin: 0;
            background-color: #f9f9f9;
          }
        </style>
      </head>
      <body>
        <div id="root"></div>

        <script>
          // Business Info Initialization
          const businessInfo = ${JSON.stringify(businessInfo)};
          console.log("Business Info:", businessInfo);

          // Helper to load scripts dynamically
          function loadScript(url, callback) {
            const script = document.createElement('script');
            script.src = url;
            script.onload = callback;
            script.onerror = () => console.error('Failed to load script:', url);
            document.head.appendChild(script);
          }

          // Initialize checkout
          function initializeCheckout() {
            if (typeof window.initializeCheckout === 'function') {
              console.log('Initializing checkout...');
              window.initializeCheckout(businessInfo);
            } else {
              console.error('initializeCheckout function is not available.');
            }
          }

          // Load scripts and initialize
          window.onload = function () {
            loadScript('https://checkout.ordrz.com/static/js/main.b75be690.js', () => {
              loadScript('https://checkout.ordrz.com/static/js/179.c3afeb96.chunk.js', initializeCheckout);
            });
          };
        </script>
      </body>
    </html>
  `;
  };

  const handleWebViewMessage = (event: any) => {
    try {
      console.log('handleWebViewMessage try');
      const messageData = JSON.parse(event.nativeEvent.data);

      console.log('22222');

      if (messageData.action === 'RETURN_TO_MENU') {
        console.log('33333');
        dispatch(resetUniqueOrderId());
        dispatch(resetCartData());
        // Navigate back to the menu screen
        navigation.navigate('Home'); // Pass businessInfo to the Checkout screen
        console.log('back in checkout screen component');
      }
    } catch (error) {
      console.log(
        'handleWebViewMessage catch = ',
        JSON.parse(event.nativeEvent.data),
      );

      // Alert.alert('Error', 'Failed to process the message from the web view.');
    }
  };

  // Function to handle cookie persistence
  const saveCookies = async () => {
    try {
      if (Platform.OS === 'android') {
        // For Android, cookies are automatically persisted by the WebView
        console.log('Cookies are automatically saved on Android');
      } else if (Platform.OS === 'ios') {
        // For iOS, cookies are also automatically persisted with sharedCookiesEnabled
        console.log('Cookies are saved with sharedCookiesEnabled on iOS');
      }
    } catch (error) {
      console.error('Error saving cookies:', error);
    }
  };

  // Save cookies when component unmounts
  useEffect(() => {
    return () => {
      saveCookies();
    };
  }, []);

  return (
    // <SafeAreaView style={styles.container}>
    <View style={styles.container}>
      {isLoading && (
        <ActivityIndicator
          size="large"
          color="#0000ff"
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            marginLeft: -25,
            marginTop: -25,
          }}
        />
      )}
      <WebView
        // key={key}
        originWhitelist={['*']}
        source={{html: getHtmlContent()}}
        // source={{ uri: checkoutUrl }} // Micro frontend
        style={styles.webview}
        startInLoadingState={true}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        sharedCookiesEnabled={true}
        thirdPartyCookiesEnabled={true}
        cacheEnabled={true}
        cacheMode="LOAD_CACHE_ELSE_NETWORK"
        onLoad={() => {
          console.log('WebView content loaded');
          setIsLoading(false);
        }}
        onError={syntheticEvent => {
          const {nativeEvent} = syntheticEvent;
          console.warn('WebView error: ', nativeEvent);
          setIsLoading(false);
          Alert.alert(
            'Error',
            'Failed to load checkout page. Please try again.',
          );
        }}
        onHttpError={syntheticEvent => {
          const {nativeEvent} = syntheticEvent;
          console.warn('HTTP error: ', nativeEvent.statusCode);
          setIsLoading(false);
          Alert.alert('Error', `HTTP error: ${nativeEvent.statusCode}`);
        }}
        onMessage={handleWebViewMessage}
        onLoadProgress={({nativeEvent}) => {
          if (nativeEvent.progress === 1) {
            console.log('WebView fully loaded');
          }
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 20,
  },
  webview: {
    marginTop: 10,
    marginBottom: 10,
    flex: 1,
  },
});

export default CheckoutScreen;
