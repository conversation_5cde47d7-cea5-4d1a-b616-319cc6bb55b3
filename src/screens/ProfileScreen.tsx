'use client';
import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import {MaterialIcons} from '@expo/vector-icons';
import {useNavigation} from '@react-navigation/native';
import type {StackNavigationProp} from '@react-navigation/stack';
import {useSelector, useDispatch} from 'react-redux';
import {logout, loginSuccess} from '../store/authSlice';
import {BRAND_ID, API_ENDPOINTS} from '../config';
import type {RootState} from '../store';

// Define the navigation stack type
type RootStackParamList = {
  Home: undefined;
  ItemDetail: {item: any};
  Search: undefined;
  ShopStack: {selectedCategoryId?: string} | undefined;
  Checkout: undefined;
  Profile: undefined;
  Login: undefined;
  ForgotPasswordScreen: undefined;
  Splash: undefined;
  OrderDetail: {orderId: string};
  Notifications: undefined;
  NotificationSettings: undefined;
};

// Define form data interface
interface FormData {
  name: string;
  email: string;
  phone: string;
}

const ProfileScreen = () => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const dispatch = useDispatch();
  const {userData, isLoggedIn} = useSelector((state: RootState) => state.auth);

  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // Form state for editing
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
  });

  // Check if user is logged in
  useEffect(() => {
    if (!isLoggedIn) {
      navigation.replace('Login');
    } else {
      // Fetch the latest profile data
      fetchProfileData();
    }
  }, [isLoggedIn, navigation]);

  // Fetch profile data from API
  const fetchProfileData = async () => {
    if (!userData?.user_email) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    try {
      // Create form data for x-www-form-urlencoded format
      const formData = new FormData();
      formData.append('user_email', userData.user_email);
      formData.append('brand_id', BRAND_ID);

      // Make API call to get profile
      const response = await fetch(API_ENDPOINTS.GET_PROFILE, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = await response.json();
      console.log('Get profile response:', data);

      if (data.status === '1' && data.result) {
        // Update Redux store with the latest user data
        const updatedUserData = {
          ...userData,
          user_id: data.result.user_id,
          user_fullname: data.result.user_fullname,
          user_gender: data.result.user_gender,
          user_address: data.result.user_address,
          user_dob: data.result.user_dob,
          user_cphone: data.result.user_cphone,
          td_user_id: data.result.td_user_id,
          user_city: data.result.user_city,
        };

        dispatch(loginSuccess(updatedUserData));

        // Update form data
        setFormData({
          name: data.result.user_fullname || '',
          email: data.result.user_email || '',
          phone: data.result.user_cphone || '',
        });
      } else {
        console.warn('Failed to get profile data:', data.message);
      }
    } catch (error) {
      console.error('Error fetching profile data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Reset form data when editing mode changes
  useEffect(() => {
    if (isEditing) {
      setFormData({
        name: userData?.user_fullname || '',
        email: userData?.user_email || '',
        phone: userData?.user_cphone || '',
      });
    }
  }, [isEditing, userData]);

  // Add back icon on the Profile screen
  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: 'Profile',
      headerStyle: {
        backgroundColor: '#FFFFFF',
        elevation: 0,
        shadowOpacity: 0,
      },
      headerTintColor: '#000000',
      headerLeft: () => (
        <TouchableOpacity
          style={{marginLeft: 16}}
          onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  const handleSave = async () => {
    setIsSaving(true);

    try {
      // Create form data for x-www-form-urlencoded format
      const formDataToSend = new FormData();
      formDataToSend.append('user_fullname', formData.name);
      formDataToSend.append('user_email', userData?.user_email || '');
      formDataToSend.append('user_cphone', formData.phone);
      formDataToSend.append('brand_id', BRAND_ID);
      formDataToSend.append('user_address', userData?.user_address || '');
      formDataToSend.append('user_dob', userData?.user_dob || '');
      formDataToSend.append('user_city', userData?.user_city || '');

      // Make API call to update profile
      const response = await fetch(API_ENDPOINTS.UPDATE_PROFILE, {
        method: 'POST',
        body: formDataToSend,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = await response.json();
      console.log('Update profile response:', data);

      if (data.status === '1') {
        // Update Redux store with new user data
        dispatch(
          loginSuccess({
            ...userData,
            user_fullname: data.result.user_fullname,
            user_cphone: data.result.user_cphone,
            // Keep other fields from the response
            ...data.result,
          }),
        );

        setIsEditing(false);
        // Show success message from API
        Alert.alert('Success', data.message || 'Profile updated successfully');

        // Refresh profile data
        fetchProfileData();
      } else {
        // Show error message
        Alert.alert('Error', data.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Network error. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setFormData({
      name: userData?.user_fullname || '',
      email: userData?.user_email || '',
      phone: userData?.user_cphone || '',
    });
  };

  const handleInputChange = (field: keyof FormData, value: string): void => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleLogout = () => {
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Logout',
        onPress: () => {
          // This will clear AsyncStorage through the authSlice
          dispatch(logout());
          navigation.replace('Login');
        },
      },
    ]);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#000000" />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <Text style={styles.avatarText}>
              {userData?.user_fullname?.charAt(0) || 'U'}
            </Text>
          </View>
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>
              {userData?.user_fullname || 'User'}
            </Text>
            <Text style={styles.profileEmail}>
              {userData?.user_email || '<EMAIL>'}
            </Text>
            <Text style={styles.profileNumber}>
              {userData?.user_cphone || 'No phone number'}
            </Text>
          </View>
          {!isEditing ? (
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setIsEditing(true)}>
              <MaterialIcons name="edit" size={24} color="#000000" />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancel}>
              <MaterialIcons name="close" size={24} color="#FF3B30" />
            </TouchableOpacity>
          )}
        </View>

        {/* Profile Form */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Personal Information</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Full Name</Text>
            {isEditing ? (
              <TextInput
                style={styles.input}
                value={formData.name}
                onChangeText={text => handleInputChange('name', text)}
                placeholder="Enter your full name"
              />
            ) : (
              <Text style={styles.value}>
                {userData?.user_fullname || 'Not provided'}
              </Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Email Address</Text>
            {isEditing ? (
              <TextInput
                style={[styles.input, styles.readOnlyInput]}
                value={formData.email}
                editable={false}
                placeholder="Enter your email address"
                keyboardType="email-address"
                autoCapitalize="none"
              />
            ) : (
              <Text style={styles.value}>
                {userData?.user_email || 'Not provided'}
              </Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Phone Number</Text>
            {isEditing ? (
              <TextInput
                style={styles.input}
                value={formData.phone}
                onChangeText={text => handleInputChange('phone', text)}
                placeholder="Enter your phone number"
                keyboardType="phone-pad"
              />
            ) : (
              <Text style={styles.value}>
                {userData?.user_cphone || 'Not provided'}
              </Text>
            )}
          </View>

          {isEditing && (
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSave}
              disabled={isSaving}>
              {isSaving ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.saveButtonText}>Update</Text>
              )}
            </TouchableOpacity>
          )}
        </View>

        {/* Logout Button - Only show when not editing */}
        {!isEditing && (
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <MaterialIcons name="logout" size={24} color="#FF3B30" />
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        )}

        {/* Bottom spacing */}
        <View style={{height: 40}} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  container: {
    flex: 1,
    padding: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  avatarContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: '#666666',
  },
  editButton: {
    padding: 8,
  },
  cancelButton: {
    padding: 8,
  },
  formContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  value: {
    fontSize: 16,
    color: '#000000',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  input: {
    fontSize: 16,
    color: '#000000',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#CCCCCC',
  },
  readOnlyInput: {
    backgroundColor: '#F5F5F5',
    color: '#666666',
  },
  saveButton: {
    backgroundColor: '#000000',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingVertical: 14,
    marginTop: 24,
  },
  logoutButtonText: {
    color: '#FF3B30',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  profileNumber: {
    fontSize: 12,
    color: '#888888',
    marginTop: 2,
  },
});

export default ProfileScreen;
