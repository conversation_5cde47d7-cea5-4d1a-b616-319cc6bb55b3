'use client';
import React, {useState} from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  TextInput,
  ToastAndroid,
  Platform,
  Alert,
} from 'react-native';
import {MaterialIcons} from '@expo/vector-icons';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import type {StackNavigationProp} from '@react-navigation/stack';
import {useAddToCart, formatPrice} from '../utils/cartUtils';
import type {RootState} from '../store';

const {width} = Dimensions.get('window');

// Define the navigation stack type
type RootStackParamList = {
  Home: undefined;
  ItemDetail: {item: any};
  Search: undefined;
  ShopStack: {selectedCategoryId?: string} | undefined;
  Checkout: undefined;
  Profile: undefined;
  Login: undefined;
  ForgotPasswordScreen: undefined;
  Splash: undefined;
  OrderDetail: {orderId: string};
  Notifications: undefined;
  NotificationSettings: undefined;
};

// Define product interface
interface Product {
  menu_item_id: string;
  name: string;
  price: number;
  discount?: number;
  desc?: string;
  image: string;
  options?: any[];
  menu_cat_id: string;
  category: string;
  category_id?: string;
  category_name?: string;
  total?: string;
}

// Helper function to strip HTML tags
const stripHtmlTags = (html: string): string => {
  if (!html) return '';
  return html.replace(/(<([^>]+)>)/gi, '');
};

const SearchScreen = () => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();

  // State
  const [searchText, setSearchText] = useState<string>('');
  const [loadingItems, setLoadingItems] = useState<Record<string, boolean>>({});
  const [showToast, setShowToast] = useState<boolean>(false);

  // Redux state
  const {allItems, currency, logo} = useSelector(
    (state: RootState) => state.order,
  );

  // Use the useAddToCart hook
  const {addToCart} = useAddToCart();

  // Filter items based on search text
  const filteredItems = searchText.trim()
    ? allItems.filter((item: Product) =>
        item.name.toLowerCase().includes(searchText.toLowerCase()),
      )
    : [];

  // Handle add to cart
  const handleAddToCart = async (product: Product): Promise<void> => {
    setLoadingItems(prevState => ({
      ...prevState,
      [product.menu_item_id]: true,
    }));

    // Check if the item has options
    const hasValidOptions = product.options && product.options.length > 0;

    if (hasValidOptions) {
      // If item has options, navigate to detail screen
      navigation.navigate('ItemDetail', {item: product});
      setLoadingItems(prevState => ({
        ...prevState,
        [product.menu_item_id]: false,
      }));
      return;
    }

    // If no options, add directly to cart
    const result = await addToCart(product, 'add', 'new');

    if (!result.success) {
      Alert.alert('Error', result.message);
    } else {
      // Show toast message
      if (Platform.OS === 'android') {
        ToastAndroid.show('Item added successfully', ToastAndroid.SHORT);
      } else {
        setShowToast(true);
        setTimeout(() => setShowToast(false), 2000);
      }
    }

    setLoadingItems(prevState => ({
      ...prevState,
      [product.menu_item_id]: false,
    }));
  };

  // Render product item
  const renderProductItem = ({
    item,
    index,
  }: {
    item: Product;
    index: number;
  }): React.ReactElement => {
    const isItemLoading = loadingItems[item.menu_item_id];
    const hasDiscount = item.discount && item.discount > 0;
    const discountedPrice =
      hasDiscount && item.discount
        ? item.price - item.price * (item.discount / 100)
        : item.price;

    return (
      <TouchableOpacity
        key={item.menu_item_id || index}
        style={styles.productCard}
        onPress={() => navigation.navigate('ItemDetail', {item})}
        activeOpacity={0.9}>
        {/* Product Image */}
        <View style={styles.productImageContainer}>
          {item.image && !item.image.includes('no_image') ? (
            <Image
              source={{uri: item.image}}
              style={styles.productImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.noImageContainer}>
              {logo ? (
                <Image
                  source={{uri: logo}}
                  style={{width: 40, height: 40}}
                  resizeMode="contain"
                />
              ) : (
                <MaterialIcons name="image" size={30} color="#666" />
              )}
            </View>
          )}
        </View>

        {/* Product Title */}
        <Text
          style={styles.productTitle}
          numberOfLines={1}
          ellipsizeMode="tail">
          {item.name}
        </Text>

        {/* Product Description */}
        {item.desc ? (
          <Text
            style={styles.productDescription}
            numberOfLines={2}
            ellipsizeMode="tail">
            {stripHtmlTags(item.desc)}
          </Text>
        ) : (
          <View style={{height: 0}} /> // Zero height placeholder when no description
        )}

        {/* Price and Add Button Row */}
        <View style={styles.priceActionRow}>
          <View style={styles.priceContainer}>
            {hasDiscount && (
              <Text style={styles.originalPrice}>
                {formatPrice(item.price, currency, 0)}
              </Text>
            )}
            <Text style={styles.currentPrice}>
              {formatPrice(discountedPrice, currency, 0)}
            </Text>
          </View>

          <TouchableOpacity
            style={styles.addButton}
            onPress={e => {
              e.stopPropagation();
              handleAddToCart(item);
            }}
            disabled={isItemLoading}>
            {isItemLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <MaterialIcons name="add" size={20} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Header with back button and search input */}
      <View style={styles.headerContainer}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}>
          <MaterialIcons name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>

        <View style={styles.searchInputContainer}>
          <MaterialIcons
            name="search"
            size={24}
            color="#777777"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Search products"
            placeholderTextColor="#999999"
            value={searchText}
            onChangeText={setSearchText}
            autoFocus={true}
            returnKeyType="search"
            clearButtonMode="while-editing"
          />
          {searchText.length > 0 && (
            <TouchableOpacity
              onPress={() => setSearchText('')}
              style={styles.clearButton}>
              <MaterialIcons name="close" size={20} color="#777777" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Results or Empty State */}
      {searchText.trim() === '' ? (
        <View style={styles.emptyStateContainer}>
          <MaterialIcons name="search" size={64} color="#CCCCCC" />
          <Text style={styles.emptyStateText}>Type to search products</Text>
        </View>
      ) : filteredItems.length > 0 ? (
        <FlatList
          data={filteredItems}
          renderItem={renderProductItem}
          keyExtractor={item => item.menu_item_id}
          numColumns={2}
          contentContainerStyle={styles.productGrid}
          columnWrapperStyle={styles.productRow}
        />
      ) : (
        <View style={styles.emptyStateContainer}>
          <MaterialIcons name="search-off" size={64} color="#CCCCCC" />
          <Text style={styles.emptyStateText}>No items found</Text>
        </View>
      )}

      {/* Toast Message */}
      {showToast && (
        <View style={styles.toast}>
          <Text style={styles.toastText}>Item added successfully</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
    padding: 0,
    height: 40,
  },
  clearButton: {
    padding: 4,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 50,
  },
  emptyStateText: {
    fontSize: 18,
    color: '#666666',
    marginTop: 16,
    fontWeight: '500',
  },
  productGrid: {
    padding: 8,
  },
  productRow: {
    justifyContent: 'space-between',
  },
  productCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    overflow: 'hidden',
    width: (width - 36) / 2, // Adjust for padding and gap
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    margin: 1,
  },
  productImageContainer: {
    width: '100%',
    height: 140,
    backgroundColor: '#F8F8F8',
  },
  productImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  noImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  productTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginTop: 8,
    marginBottom: 4,
    paddingHorizontal: 8,
  },
  productDescription: {
    fontSize: 12,
    color: '#666666',
    paddingHorizontal: 8,
    marginBottom: 4,
    lineHeight: 16,
    height: 32,
  },
  priceActionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingBottom: 8,
    marginTop: 16,
  },
  priceContainer: {
    flexDirection: 'column',
  },
  originalPrice: {
    fontSize: 14,
    color: '#888888',
    textDecorationLine: 'line-through',
  },
  currentPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  toast: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 128, 0, 0.8)',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  toastText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default SearchScreen;
