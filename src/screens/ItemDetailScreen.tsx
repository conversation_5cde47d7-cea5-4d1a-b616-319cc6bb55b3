'use client';

import type React from 'react';
import {useState, useEffect, useMemo, useRef} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
  Platform,
  StatusBar,
  Animated,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useSelector} from 'react-redux';
import {formatPrice, useAddToCart} from '../utils/cartUtils';
import {useTheme} from '../theme/ThemeProvider';
import OptionSetRenderer from '../components/OptionSetRenderer';
import {checkBusinessHours} from '../utils/businessHoursUtils';

const {width, height} = Dimensions.get('window');
const STATUSBAR_HEIGHT =
  Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

// Define interfaces for the component props and state
interface RouteParams {
  item: any;
}

interface NavigationProps {
  navigate: (screen: string, params?: any) => void;
  goBack: () => void;
  replace: (screen: string, params?: any) => void;
}

interface ItemDetailScreenProps {
  route: {
    params: RouteParams;
  };
  navigation: NavigationProps;
}

interface RootState {
  order: {
    currency: string;
    businessHours: any[];
  };
}

const ItemDetailScreen: React.FC<ItemDetailScreenProps> = ({
  route,
  navigation,
}) => {
  const {item} = route.params;
  const {theme} = useTheme();
  const scrollViewRef = useRef<ScrollView>(null);

  // Animation for error message
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [errorMessage, setErrorMessage] = useState('');

  // State
  const [quantity, setQuantity] = useState(1);
  const [selectedOptions, setSelectedOptions] = useState<any>({});
  const [expandedOptions, setExpandedOptions] = useState<{
    [key: string]: boolean;
  }>({});
  const [highlightedOptions, setHighlightedOptions] = useState<string[]>([]);
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  // Redux state
  const {currency, businessHours} = useSelector(
    (state: RootState) => state.order,
  );

  // Use the useAddToCart hook
  const {addToCart, isLoading} = useAddToCart();

  // Initialize option set details
  useEffect(() => {
    if (item && item.options) {
      initializeOptionSets();

      // Expand all option sets by default
      const initialExpandedState: {[key: string]: boolean} = {};
      item.options.forEach((option: any) => {
        initialExpandedState[option.id] = true;
      });
      setExpandedOptions(initialExpandedState);
    }
  }, [item]);

  // Handle error message animation
  useEffect(() => {
    if (errorMessage) {
      // Fade in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Auto hide after 3 seconds
      const timer = setTimeout(() => {
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start(() => setErrorMessage(''));
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [errorMessage, fadeAnim]);

  // Initialize option sets without default selections
  const initializeOptionSets = () => {
    if (!item || !item.options) return;

    const initialOptions: {[key: string]: any} = {};

    item.options.forEach((optionSet: any) => {
      const isRequired = optionSet.min_quantity !== '0';
      const isMultiSelect =
        optionSet.quantity > 1 || optionSet.quantity === '0';

      initialOptions[optionSet.id] = {
        name: optionSet.name,
        isRequired,
        isMultiSelect,
        items: {},
      };
    });

    setSelectedOptions(initialOptions);
  };

  // Update quantity
  const updateQuantity = (change: number) => {
    setQuantity(prevQuantity => Math.max(1, prevQuantity + change));
  };

  // Toggle option expansion
  const toggleOption = (optionId: string) => {
    setExpandedOptions(prev => ({
      ...prev,
      [optionId]: !prev[optionId],
    }));
  };

  // Handle option selection
  const handleOptionSelect = (optionId: string, itemId: string) => {
    // Clear highlighted options when user makes a selection
    setHighlightedOptions([]);

    setSelectedOptions((prev: any) => {
      const updatedOptions = {...prev};
      const optionSet = updatedOptions[optionId];

      // If single select (radio button), replace previous selection
      if (!optionSet.isMultiSelect) {
        updatedOptions[optionId] = {
          ...optionSet,
          items: {[itemId]: 1},
        };
      }
      // If multi-select (checkbox)
      else {
        // If already selected, unselect it
        if (optionSet.items[itemId]) {
          const {[itemId]: _, ...remainingItems} = optionSet.items;
          updatedOptions[optionId] = {
            ...optionSet,
            items: remainingItems,
          };
        }
        // Otherwise select it
        else {
          updatedOptions[optionId] = {
            ...optionSet,
            items: {
              ...optionSet.items,
              [itemId]: 1,
            },
          };
        }
      }

      return updatedOptions;
    });
  };

  // Check if option set requirement is met
  const isOptionSetRequirementMet = (optionSetId: string) => {
    const optionSet = item.options.find((opt: any) => opt.id === optionSetId);
    const selectedItems = selectedOptions[optionSetId]?.items || {};
    const selectedCount = Object.keys(selectedItems).length;

    if (!optionSet) return true;

    const minRequired = Number.parseInt(optionSet.min_quantity || '0');
    return selectedCount >= minRequired;
  };

  // Validate options before adding to cart
  const validateOptions = () => {
    const invalidOptions: {id: string; name: string}[] = [];

    Object.entries(selectedOptions).forEach(
      ([optionId, optionData]: [string, any]) => {
        if (
          optionData.isRequired &&
          Object.keys(optionData.items).length === 0
        ) {
          invalidOptions.push({
            id: optionId,
            name: optionData.name,
          });
        }
      },
    );

    return invalidOptions;
  };

  // Scroll to a specific option set
  const scrollToOption = (optionId: string) => {
    if (scrollViewRef.current && item.options) {
      // Find the index of the option set
      const index = item.options.findIndex((opt: any) => opt.id === optionId);
      if (index !== -1) {
        // Calculate approximate position (this is an estimation)
        const optionPosition = 400 + index * 150; // Adjusted for new layout
        scrollViewRef.current.scrollTo({y: optionPosition, animated: true});
      }
    }
  };

  // Add to cart handler
  const handleAddToCart = async () => {
    // Check if business is closed
    if (checkBusinessHours(businessHours)) {
      return; // Don't proceed if business is closed
    }

    const invalidOptions = validateOptions();

    if (invalidOptions.length > 0) {
      // Set error message
      setErrorMessage(
        `Please select required options: ${invalidOptions
          .map(opt => opt.name)
          .join(', ')}`,
      );

      // Highlight the invalid options
      setHighlightedOptions(invalidOptions.map(opt => opt.id));

      // Scroll to the first invalid option
      if (invalidOptions.length > 0) {
        scrollToOption(invalidOptions[0].id);
      }

      return;
    }

    // Format selected options for API
    const formattedOptions: {[key: string]: any[]} = {};

    item.options.forEach((optionSet: any) => {
      const selectedItems = selectedOptions[optionSet.id]?.items || {};
      const optionItems: any[] = [];

      optionSet.items.forEach((optItem: any) => {
        if (selectedItems[optItem.id]) {
          optionItems.push({
            name: optItem.name,
            price: optItem.price,
            quantity: selectedItems[optItem.id],
            inner_options: [],
          });
        }
      });

      if (optionItems.length > 0) {
        formattedOptions[optionSet.name] = optionItems;
      }
    });

    const result = await addToCart(
      item,
      'add',
      'new',
      JSON.stringify(formattedOptions),
      quantity,
    );

    if (!result.success) {
      Alert.alert('Error', result.message);
    } else {
      // Navigate back after successful addition
      navigation.goBack();
    }
  };

  // Calculate grand total
  const grandTotal = useMemo(() => {
    if (!item) return 0;

    let total = Number(item.price) || 0;

    // Add option prices
    if (item.options) {
      item.options.forEach((optionSet: any) => {
        const selectedItems = selectedOptions[optionSet.id]?.items || {};

        optionSet.items.forEach((optItem: any) => {
          if (selectedItems[optItem.id]) {
            total += Number(optItem.price) * selectedItems[optItem.id];
          }
        });
      });
    }

    return total * quantity;
  }, [item, quantity, selectedOptions]);

  // Strip HTML tags from description
  const stripHtmlTags = (html: string) => {
    if (!html) return '';
    return html.replace(/(<([^>]+)>)/gi, '');
  };

  if (!item) return null;

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Full-screen ScrollView */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}>
        {/* Product Image with Back Button and Cart Button Overlay */}
        <View style={styles.imageContainer}>
          <Image
            source={{uri: item.image}}
            style={styles.productImage}
            resizeMode="cover"
          />

          {/* Back button overlay */}
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.8}>
            <Icon name="arrow-back" size={24} color="#000000" />
          </TouchableOpacity>
        </View>

        {/* Product Details */}
        <View style={styles.productDetails}>
          <Text style={styles.productName}>{item.name}</Text>

          {item.desc ? (
            <>
              <Text
                style={styles.productDescription}
                numberOfLines={isDescriptionExpanded ? undefined : 2}
                ellipsizeMode="tail">
                {stripHtmlTags(item.desc)}
              </Text>

              {/* Only show Read more/less if description is long enough */}
              {item.desc.length > 80 && (
                <TouchableOpacity
                  onPress={() =>
                    setIsDescriptionExpanded(!isDescriptionExpanded)
                  }>
                  <Text style={styles.readMoreLink}>
                    {isDescriptionExpanded ? 'Read less' : 'Read more'}
                  </Text>
                </TouchableOpacity>
              )}
            </>
          ) : null}
        </View>

        {/* Option Sets */}
        {item.options &&
          item.options.map((optionSet: any) => (
            <View
              key={optionSet.id}
              style={[
                styles.optionSetContainer,
                highlightedOptions.includes(optionSet.id) &&
                  styles.highlightedOptionSet,
              ]}>
              <View style={styles.optionSetHeader}>
                <Text style={styles.optionSetTitle}>{optionSet.name}</Text>

                <View style={styles.optionSetRequirement}>
                  <Text style={styles.chooseText}>
                    Choose{' '}
                    {optionSet.min_quantity === '1'
                      ? 'any 1'
                      : `${optionSet.min_quantity}`}
                  </Text>

                  {optionSet.min_quantity !== '0' && (
                    <Text
                      style={[
                        styles.requiredText,
                        isOptionSetRequirementMet(optionSet.id)
                          ? styles.requiredTextComplete
                          : {},
                      ]}>
                      Required
                    </Text>
                  )}
                </View>
              </View>

              <OptionSetRenderer
                optionSet={optionSet}
                selectedOptions={selectedOptions}
                isExpanded={expandedOptions[optionSet.id]}
                onToggle={toggleOption}
                onSelect={handleOptionSelect}
              />
            </View>
          ))}

        {/* Add spacing at the bottom to ensure content isn't hidden behind the bottom bar */}
        <View style={{height: 100}} />
      </ScrollView>

      {/* Error Message */}
      {errorMessage !== '' && (
        <Animated.View style={[styles.errorContainer, {opacity: fadeAnim}]}>
          <Icon name="error-outline" size={20} color="#FFFFFF" />
          <Text style={styles.errorText}>{errorMessage}</Text>
        </Animated.View>
      )}

      {/* Bottom Bar with Quantity and Add to Cart */}
      <View style={styles.bottomBar}>
        <View style={styles.quantityContainer}>
          <TouchableOpacity
            style={styles.quantityButton}
            onPress={() => updateQuantity(-1)}
            disabled={quantity <= 1}>
            <Icon name="remove" size={20} color="#FFFFFF" />
          </TouchableOpacity>

          <Text style={styles.quantityText}>{quantity}</Text>

          <TouchableOpacity
            style={styles.quantityButton}
            onPress={() => updateQuantity(1)}>
            <Icon name="add" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        <View style={styles.priceContainer}>
          <Text style={styles.priceText}>
            {formatPrice(grandTotal, currency, 0)}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.addToCartButton}
          onPress={handleAddToCart}
          disabled={isLoading}>
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.addToCartText}>Add to cart</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  imageContainer: {
    width: '100%',
    height: width * 0.8, // Square image
    position: 'relative',
  },
  productImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#F8F8F8',
  },
  backButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EEEEEE',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  cartButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EEEEEE',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  errorContainer: {
    position: 'absolute',
    top: width * 0.8 + 10, // Position just below the image
    left: 16,
    right: 16,
    backgroundColor: '#FF3B30',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 100,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  errorText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  productDetails: {
    padding: 16,
  },
  productName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  productDescription: {
    fontSize: 16,
    color: '#555555',
    lineHeight: 22,
  },
  readMoreLink: {
    color: '#FF3B30',
    fontSize: 16,
    fontWeight: '500',
    marginTop: 4,
  },
  optionSetContainer: {
    marginBottom: 24,
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 4,
  },
  highlightedOptionSet: {
    backgroundColor: '#FFF5F5',
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  optionSetHeader: {
    marginBottom: 12,
  },
  optionSetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  optionSetRequirement: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chooseText: {
    fontSize: 14,
    color: '#555555',
  },
  requiredText: {
    fontSize: 14,
    color: '#FF3B30',
    fontWeight: '500',
  },
  requiredTextComplete: {
    color: '#4CAF50', // Green color when requirement is met
  },
  bottomBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 16,
  },
  priceContainer: {
    flex: 1,
  },
  priceText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
  },
  addToCartButton: {
    backgroundColor: '#000000',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  addToCartText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ItemDetailScreen;
