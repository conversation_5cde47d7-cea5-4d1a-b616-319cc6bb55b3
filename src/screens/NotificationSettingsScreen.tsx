'use client';
import {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Platform,
  ToastAndroid,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';
import type {StackNavigationProp} from '@react-navigation/stack';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the navigation stack type
type RootStackParamList = {
  Home: undefined;
  ItemDetail: {item: any};
  Search: undefined;
  ShopStack: {selectedCategoryId?: string} | undefined;
  Checkout: undefined;
  Profile: undefined;
  Login: undefined;
  ForgotPasswordScreen: undefined;
  Splash: undefined;
  OrderDetail: {orderId: string};
  Notifications: undefined;
  NotificationSettings: undefined;
};

interface RootState {
  order: {
    logo: string;
  };
  auth: {
    isLoggedIn: boolean;
    userData: any;
  };
}

const NotificationSettingsScreen = () => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const {isLoggedIn} = useSelector((state: RootState) => state.auth);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState('success');

  // Notification settings state
  const [settings, setSettings] = useState({
    orderUpdates: true,
    promotions: true,
    general: true,
    pushNotifications: true,
    emailNotifications: false,
  });

  // Set header options
  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: 'Notification Settings',
      headerStyle: {
        backgroundColor: '#FFFFFF',
        elevation: 0,
        shadowOpacity: 0,
      },
      headerTintColor: '#000000',
      headerLeft: () => (
        <TouchableOpacity
          style={{marginLeft: 16}}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  // Check if user is logged in
  useEffect(() => {
    if (!isLoggedIn) {
      navigation.replace('Login');
    } else {
      loadSettings();
    }
  }, [isLoggedIn, navigation]);

  // Load saved notification settings
  const loadSettings = async () => {
    setIsLoading(true);
    try {
      const savedSettings = await AsyncStorage.getItem('notificationSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Save notification settings
  const saveSettings = async () => {
    setIsSaving(true);
    try {
      await AsyncStorage.setItem('notificationSettings', JSON.stringify(settings));
      showToastMessage('Settings saved successfully', 'success');
    } catch (error) {
      console.error('Error saving notification settings:', error);
      showToastMessage('Failed to save settings', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Show toast message
  const showToastMessage = (message: string, type = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);

    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    }
  };

  // Toggle switch handler
  const toggleSwitch = (key: keyof typeof settings) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      [key]: !prevSettings[key],
    }));
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#000000" />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <ScrollView style={styles.container}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Types</Text>
          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingTitle}>Order Updates</Text>
              <Text style={styles.settingDescription}>
                Receive notifications about your order status
              </Text>
            </View>
            <Switch
              value={settings.orderUpdates}
              onValueChange={() => toggleSwitch('orderUpdates')}
              trackColor={{false: '#D1D1D1', true: '#000000'}}
              thumbColor={Platform.OS === 'android' ? '#FFFFFF' : ''}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingTitle}>Promotions</Text>
              <Text style={styles.settingDescription}>
                Receive notifications about special offers and discounts
              </Text>
            </View>
            <Switch
              value={settings.promotions}
              onValueChange={() => toggleSwitch('promotions')}
              trackColor={{false: '#D1D1D1', true: '#000000'}}
              thumbColor={Platform.OS === 'android' ? '#FFFFFF' : ''}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingTitle}>General</Text>
              <Text style={styles.settingDescription}>
                Receive general notifications about the app
              </Text>
            </View>
            <Switch
              value={settings.general}
              onValueChange={() => toggleSwitch('general')}
              trackColor={{false: '#D1D1D1', true: '#000000'}}
              thumbColor={Platform.OS === 'android' ? '#FFFFFF' : ''}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Channels</Text>
          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingTitle}>Push Notifications</Text>
              <Text style={styles.settingDescription}>
                Receive notifications on your device
              </Text>
            </View>
            <Switch
              value={settings.pushNotifications}
              onValueChange={() => toggleSwitch('pushNotifications')}
              trackColor={{false: '#D1D1D1', true: '#000000'}}
              thumbColor={Platform.OS === 'android' ? '#FFFFFF' : ''}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingTitle}>Email Notifications</Text>
              <Text style={styles.settingDescription}>
                Receive notifications via email
              </Text>
            </View>
            <Switch
              value={settings.emailNotifications}
              onValueChange={() => toggleSwitch('emailNotifications')}
              trackColor={{false: '#D1D1D1', true: '#000000'}}
              thumbColor={Platform.OS === 'android' ? '#FFFFFF' : ''}
            />
          </View>
        </View>

        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveSettings}
          disabled={isSaving}>
          {isSaving ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.saveButtonText}>Save Settings</Text>
          )}
        </TouchableOpacity>
      </ScrollView>

      {/* Toast Message */}
      {showToast && (
        <View
          style={[
            styles.toast,
            toastType === 'success' ? styles.successToast : styles.errorToast,
          ]}>
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666666',
  },
  saveButton: {
    backgroundColor: '#000000',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 40,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  toast: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: '#000000',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  successToast: {
    backgroundColor: '#4CAF50',
  },
  errorToast: {
    backgroundColor: '#F44336',
  },
  toastText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default NotificationSettingsScreen;
